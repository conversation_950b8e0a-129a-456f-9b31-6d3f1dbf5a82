"""
Command-line interface for tex2word package.

This module provides a user-friendly command-line interface for converting
LaTeX documents to Word format with various options and configurations.
"""

import os
import sys
import click
from pathlib import Path
from typing import Optional, Dict, Any

try:
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.table import Table
    from rich.panel import Panel
    from rich import print as rprint
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    Console = None

from .core.converter import TexToWordConverter
from .core.exceptions import TexToWordError
from .config.settings import ConfigManager


# Initialize console for rich output
console = Console() if RICH_AVAILABLE else None


@click.group()
@click.version_option(version="1.0.0", prog_name="tex2word")
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Path to configuration file')
@click.pass_context
def cli(ctx, verbose, config):
    """
    tex2word: Professional LaTeX to Word converter with elegant math formula conversion.
    
    Convert LaTeX documents to Microsoft Word format while preserving
    mathematical formulas, document structure, and formatting.
    """
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    ctx.obj['config'] = config


@cli.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.argument('output_file', type=click.Path())
@click.option('--math-method', type=click.Choice(['mathml', 'unicode', 'image']),
              default='mathml', help='Method for converting math formulas')
@click.option('--preserve-comments', is_flag=True, 
              help='Preserve LaTeX comments in output')
@click.option('--strict', is_flag=True, 
              help='Enable strict mode (fail on any error)')
@click.option('--no-numbering', is_flag=True,
              help='Disable equation and section numbering')
@click.pass_context
def convert(ctx, input_file, output_file, math_method, preserve_comments, 
           strict, no_numbering):
    """
    Convert a LaTeX file to Word format.
    
    INPUT_FILE: Path to the LaTeX file to convert
    OUTPUT_FILE: Path for the output Word document
    """
    verbose = ctx.obj.get('verbose', False)
    config_path = ctx.obj.get('config')
    
    try:
        # Initialize converter
        converter = _create_converter(config_path, {
            'math': {'conversion_method': math_method},
            'conversion': {
                'preserve_comments': preserve_comments,
                'strict_mode': strict
            },
            'styles': {
                'section': {'numbering': not no_numbering},
                'equation': {'numbering': not no_numbering}
            }
        })
        
        # Perform conversion with progress indication
        if RICH_AVAILABLE and not verbose:
            _convert_with_progress(converter, input_file, output_file)
        else:
            _convert_simple(converter, input_file, output_file, verbose)
            
    except TexToWordError as e:
        _handle_error(e, verbose)
        sys.exit(1)
    except Exception as e:
        _handle_unexpected_error(e, verbose)
        sys.exit(1)


@cli.command()
@click.argument('input_dir', type=click.Path(exists=True, file_okay=False))
@click.argument('output_dir', type=click.Path())
@click.option('--pattern', default='*.tex', 
              help='File pattern to match (default: *.tex)')
@click.option('--recursive', '-r', is_flag=True,
              help='Process files recursively')
@click.option('--parallel', '-p', type=int, default=1,
              help='Number of parallel processes (default: 1)')
@click.pass_context
def batch(ctx, input_dir, output_dir, pattern, recursive, parallel):
    """
    Convert multiple LaTeX files in batch mode.
    
    INPUT_DIR: Directory containing LaTeX files
    OUTPUT_DIR: Directory for output Word documents
    """
    verbose = ctx.obj.get('verbose', False)
    config_path = ctx.obj.get('config')
    
    try:
        # Find input files
        input_path = Path(input_dir)
        if recursive:
            tex_files = list(input_path.rglob(pattern))
        else:
            tex_files = list(input_path.glob(pattern))
        
        if not tex_files:
            click.echo(f"No files matching '{pattern}' found in {input_dir}")
            return
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize converter
        converter = _create_converter(config_path)
        
        # Process files
        _batch_convert(converter, tex_files, output_path, verbose, parallel)
        
    except Exception as e:
        _handle_unexpected_error(e, verbose)
        sys.exit(1)


@cli.command()
@click.argument('latex_file', type=click.Path(exists=True))
@click.pass_context
def validate(ctx, latex_file):
    """
    Validate a LaTeX file without conversion.
    
    LATEX_FILE: Path to the LaTeX file to validate
    """
    verbose = ctx.obj.get('verbose', False)
    config_path = ctx.obj.get('config')
    
    try:
        # Read LaTeX content
        with open(latex_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Initialize converter
        converter = _create_converter(config_path)
        
        # Validate
        results = converter.validate_latex(content)
        
        # Display results
        _display_validation_results(results, latex_file)
        
        if not results['valid']:
            sys.exit(1)
            
    except Exception as e:
        _handle_unexpected_error(e, verbose)
        sys.exit(1)


@cli.command()
@click.argument('output_file', type=click.Path())
def config(output_file):
    """
    Generate a sample configuration file.
    
    OUTPUT_FILE: Path for the sample configuration file
    """
    try:
        config_manager = ConfigManager()
        config_manager.create_sample_config(output_file)
        
        if RICH_AVAILABLE:
            rprint(f"[green]✓[/green] Sample configuration created: {output_file}")
        else:
            click.echo(f"Sample configuration created: {output_file}")
            
    except Exception as e:
        click.echo(f"Error creating configuration: {e}", err=True)
        sys.exit(1)


def _create_converter(config_path: Optional[str], 
                     custom_config: Optional[Dict[str, Any]] = None) -> TexToWordConverter:
    """Create and configure converter instance."""
    return TexToWordConverter(config_path=config_path, custom_config=custom_config)


def _convert_with_progress(converter: TexToWordConverter, 
                          input_file: str, output_file: str) -> None:
    """Convert with rich progress indication."""
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Converting...", total=None)
        
        try:
            results = converter.convert(input_file, output_file)
            progress.update(task, description="[green]✓ Conversion completed")
            
            # Display results
            _display_results(results)
            
        except Exception as e:
            progress.update(task, description="[red]✗ Conversion failed")
            raise


def _convert_simple(converter: TexToWordConverter, input_file: str, 
                   output_file: str, verbose: bool) -> None:
    """Convert with simple output."""
    if verbose:
        click.echo(f"Converting {input_file} to {output_file}...")
    
    results = converter.convert(input_file, output_file)
    
    if verbose:
        _display_results_simple(results)
    else:
        click.echo(f"Conversion completed: {output_file}")


def _batch_convert(converter: TexToWordConverter, tex_files: list, 
                  output_dir: Path, verbose: bool, parallel: int) -> None:
    """Convert multiple files."""
    total_files = len(tex_files)
    successful = 0
    failed = 0
    
    if RICH_AVAILABLE:
        with Progress(console=console) as progress:
            task = progress.add_task("Converting files...", total=total_files)
            
            for tex_file in tex_files:
                output_file = output_dir / f"{tex_file.stem}.docx"
                
                try:
                    converter.convert(str(tex_file), str(output_file))
                    successful += 1
                    if verbose:
                        rprint(f"[green]✓[/green] {tex_file.name}")
                except Exception as e:
                    failed += 1
                    if verbose:
                        rprint(f"[red]✗[/red] {tex_file.name}: {e}")
                
                progress.advance(task)
        
        # Summary
        rprint(f"\n[green]Successful:[/green] {successful}")
        rprint(f"[red]Failed:[/red] {failed}")
    else:
        for i, tex_file in enumerate(tex_files, 1):
            output_file = output_dir / f"{tex_file.stem}.docx"
            
            try:
                converter.convert(str(tex_file), str(output_file))
                successful += 1
                if verbose:
                    click.echo(f"✓ {tex_file.name}")
            except Exception as e:
                failed += 1
                if verbose:
                    click.echo(f"✗ {tex_file.name}: {e}")
            
            if not verbose:
                click.echo(f"Progress: {i}/{total_files}")
        
        click.echo(f"\nSuccessful: {successful}, Failed: {failed}")


def _display_results(results: Dict[str, Any]) -> None:
    """Display conversion results with rich formatting."""
    if not RICH_AVAILABLE:
        _display_results_simple(results)
        return
    
    stats = results.get('statistics', {})
    
    # Create statistics table
    table = Table(title="Conversion Statistics")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Total Elements", str(stats.get('total_elements', 0)))
    table.add_row("Math Elements", str(stats.get('math_elements', 0)))
    table.add_row("Sections", str(stats.get('sections', 0)))
    table.add_row("Equations", str(stats.get('total_equations', 0)))
    
    console.print(table)
    
    # Display warnings if any
    warnings = results.get('warnings', [])
    if warnings:
        warning_panel = Panel(
            "\n".join(warnings),
            title="Warnings",
            border_style="yellow"
        )
        console.print(warning_panel)


def _display_results_simple(results: Dict[str, Any]) -> None:
    """Display conversion results with simple formatting."""
    stats = results.get('statistics', {})
    
    click.echo("\nConversion Statistics:")
    click.echo(f"  Total Elements: {stats.get('total_elements', 0)}")
    click.echo(f"  Math Elements: {stats.get('math_elements', 0)}")
    click.echo(f"  Sections: {stats.get('sections', 0)}")
    click.echo(f"  Equations: {stats.get('total_equations', 0)}")
    
    warnings = results.get('warnings', [])
    if warnings:
        click.echo("\nWarnings:")
        for warning in warnings:
            click.echo(f"  - {warning}")


def _display_validation_results(results: Dict[str, Any], filename: str) -> None:
    """Display validation results."""
    if RICH_AVAILABLE:
        if results['valid']:
            rprint(f"[green]✓[/green] {filename} is valid")
        else:
            rprint(f"[red]✗[/red] {filename} has errors")
        
        # Display statistics
        stats = results.get('statistics', {})
        if stats:
            table = Table(title="Document Statistics")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            for key, value in stats.items():
                table.add_row(key.replace('_', ' ').title(), str(value))
            
            console.print(table)
        
        # Display errors and warnings
        errors = results.get('errors', [])
        warnings = results.get('warnings', [])
        
        if errors:
            error_panel = Panel(
                "\n".join(errors),
                title="Errors",
                border_style="red"
            )
            console.print(error_panel)
        
        if warnings:
            warning_panel = Panel(
                "\n".join(warnings),
                title="Warnings", 
                border_style="yellow"
            )
            console.print(warning_panel)
    else:
        if results['valid']:
            click.echo(f"✓ {filename} is valid")
        else:
            click.echo(f"✗ {filename} has errors")
        
        errors = results.get('errors', [])
        warnings = results.get('warnings', [])
        
        if errors:
            click.echo("\nErrors:")
            for error in errors:
                click.echo(f"  - {error}")
        
        if warnings:
            click.echo("\nWarnings:")
            for warning in warnings:
                click.echo(f"  - {warning}")


def _handle_error(error: TexToWordError, verbose: bool) -> None:
    """Handle tex2word specific errors."""
    if RICH_AVAILABLE:
        rprint(f"[red]Error:[/red] {error.message}")
        if verbose and error.details:
            rprint(f"[yellow]Details:[/yellow] {error.details}")
    else:
        click.echo(f"Error: {error.message}", err=True)
        if verbose and error.details:
            click.echo(f"Details: {error.details}", err=True)


def _handle_unexpected_error(error: Exception, verbose: bool) -> None:
    """Handle unexpected errors."""
    if RICH_AVAILABLE:
        rprint(f"[red]Unexpected error:[/red] {str(error)}")
        if verbose:
            import traceback
            rprint(f"[yellow]Traceback:[/yellow]\n{traceback.format_exc()}")
    else:
        click.echo(f"Unexpected error: {str(error)}", err=True)
        if verbose:
            import traceback
            click.echo(f"Traceback:\n{traceback.format_exc()}", err=True)


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
