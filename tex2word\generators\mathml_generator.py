"""
MathML generator for tex2word package.

This module provides functionality to generate and manipulate MathML
content for Word document integration.
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any
from ..converters.math_converter import MathMLElement


class MathMLGenerator:
    """Generator for MathML content."""
    
    def __init__(self):
        """Initialize the MathML generator."""
        self.namespace = "http://www.w3.org/1998/Math/MathML"
        self.word_namespace = "http://schemas.openxmlformats.org/officeDocument/2006/math"
    
    def generate_word_math(self, mathml_element: MathMLElement) -> str:
        """
        Generate Word-compatible math XML from MathML.
        
        Args:
            mathml_element: MathMLElement to convert
            
        Returns:
            Word-compatible math XML string
        """
        try:
            # Parse the MathML
            mathml_root = ET.fromstring(mathml_element.mathml_content)
            
            # Convert to Word math format
            word_math = self._convert_mathml_to_word_math(mathml_root)
            
            return ET.tostring(word_math, encoding='unicode')
            
        except Exception:
            # Fallback: return simple text representation
            return self._create_fallback_math(mathml_element.original_latex)
    
    def _convert_mathml_to_word_math(self, mathml_root: ET.Element) -> ET.Element:
        """
        Convert MathML to Word math format.
        
        Args:
            mathml_root: Root MathML element
            
        Returns:
            Word math XML element
        """
        # Create Word math root element
        math_element = ET.Element(f"{{{self.word_namespace}}}oMath")
        
        # Convert MathML elements to Word math elements
        self._convert_mathml_children(mathml_root, math_element)
        
        return math_element
    
    def _convert_mathml_children(self, mathml_parent: ET.Element, 
                                word_parent: ET.Element) -> None:
        """
        Convert MathML children to Word math children.
        
        Args:
            mathml_parent: Parent MathML element
            word_parent: Parent Word math element
        """
        for child in mathml_parent:
            word_child = self._convert_mathml_element(child)
            if word_child is not None:
                word_parent.append(word_child)
    
    def _convert_mathml_element(self, mathml_element: ET.Element) -> Optional[ET.Element]:
        """
        Convert a single MathML element to Word math element.
        
        Args:
            mathml_element: MathML element to convert
            
        Returns:
            Converted Word math element or None
        """
        tag = mathml_element.tag.split('}')[-1] if '}' in mathml_element.tag else mathml_element.tag
        
        if tag == 'mi':  # Identifier
            return self._create_word_text(mathml_element.text or '')
        elif tag == 'mn':  # Number
            return self._create_word_text(mathml_element.text or '')
        elif tag == 'mo':  # Operator
            return self._create_word_text(mathml_element.text or '')
        elif tag == 'mtext':  # Text
            return self._create_word_text(mathml_element.text or '')
        elif tag == 'msup':  # Superscript
            return self._create_word_superscript(mathml_element)
        elif tag == 'msub':  # Subscript
            return self._create_word_subscript(mathml_element)
        elif tag == 'mfrac':  # Fraction
            return self._create_word_fraction(mathml_element)
        elif tag == 'msqrt':  # Square root
            return self._create_word_radical(mathml_element)
        else:
            # For unsupported elements, try to extract text content
            text_content = self._extract_text_content(mathml_element)
            if text_content:
                return self._create_word_text(text_content)
        
        return None
    
    def _create_word_text(self, text: str) -> ET.Element:
        """Create Word math text element."""
        run = ET.Element(f"{{{self.word_namespace}}}r")
        text_elem = ET.SubElement(run, f"{{{self.word_namespace}}}t")
        text_elem.text = text
        return run
    
    def _create_word_superscript(self, mathml_element: ET.Element) -> ET.Element:
        """Create Word math superscript element."""
        sup = ET.Element(f"{{{self.word_namespace}}}sSup")
        
        # Base
        base = ET.SubElement(sup, f"{{{self.word_namespace}}}e")
        if len(mathml_element) > 0:
            base_elem = self._convert_mathml_element(mathml_element[0])
            if base_elem is not None:
                base.append(base_elem)
        
        # Superscript
        sup_elem = ET.SubElement(sup, f"{{{self.word_namespace}}}sup")
        if len(mathml_element) > 1:
            sup_content = self._convert_mathml_element(mathml_element[1])
            if sup_content is not None:
                sup_elem.append(sup_content)
        
        return sup
    
    def _create_word_subscript(self, mathml_element: ET.Element) -> ET.Element:
        """Create Word math subscript element."""
        sub = ET.Element(f"{{{self.word_namespace}}}sSub")
        
        # Base
        base = ET.SubElement(sub, f"{{{self.word_namespace}}}e")
        if len(mathml_element) > 0:
            base_elem = self._convert_mathml_element(mathml_element[0])
            if base_elem is not None:
                base.append(base_elem)
        
        # Subscript
        sub_elem = ET.SubElement(sub, f"{{{self.word_namespace}}}sub")
        if len(mathml_element) > 1:
            sub_content = self._convert_mathml_element(mathml_element[1])
            if sub_content is not None:
                sub_elem.append(sub_content)
        
        return sub
    
    def _create_word_fraction(self, mathml_element: ET.Element) -> ET.Element:
        """Create Word math fraction element."""
        frac = ET.Element(f"{{{self.word_namespace}}}f")
        
        # Numerator
        num = ET.SubElement(frac, f"{{{self.word_namespace}}}num")
        if len(mathml_element) > 0:
            num_elem = self._convert_mathml_element(mathml_element[0])
            if num_elem is not None:
                num.append(num_elem)
        
        # Denominator
        den = ET.SubElement(frac, f"{{{self.word_namespace}}}den")
        if len(mathml_element) > 1:
            den_elem = self._convert_mathml_element(mathml_element[1])
            if den_elem is not None:
                den.append(den_elem)
        
        return frac
    
    def _create_word_radical(self, mathml_element: ET.Element) -> ET.Element:
        """Create Word math radical (square root) element."""
        rad = ET.Element(f"{{{self.word_namespace}}}rad")
        
        # Radicand
        e = ET.SubElement(rad, f"{{{self.word_namespace}}}e")
        if len(mathml_element) > 0:
            rad_elem = self._convert_mathml_element(mathml_element[0])
            if rad_elem is not None:
                e.append(rad_elem)
        
        return rad
    
    def _extract_text_content(self, element: ET.Element) -> str:
        """Extract all text content from an element and its children."""
        text_parts = []
        
        if element.text:
            text_parts.append(element.text)
        
        for child in element:
            child_text = self._extract_text_content(child)
            if child_text:
                text_parts.append(child_text)
            
            if child.tail:
                text_parts.append(child.tail)
        
        return ''.join(text_parts)
    
    def _create_fallback_math(self, latex_content: str) -> str:
        """Create fallback math representation."""
        # Simple fallback: wrap in Word math structure with text
        math_elem = ET.Element(f"{{{self.word_namespace}}}oMath")
        run = ET.SubElement(math_elem, f"{{{self.word_namespace}}}r")
        text_elem = ET.SubElement(run, f"{{{self.word_namespace}}}t")
        text_elem.text = latex_content
        
        return ET.tostring(math_elem, encoding='unicode')
    
    def validate_mathml(self, mathml_content: str) -> bool:
        """
        Validate MathML content.
        
        Args:
            mathml_content: MathML content to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            ET.fromstring(mathml_content)
            return True
        except ET.ParseError:
            return False
