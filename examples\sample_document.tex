\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{amsthm}
\usepackage{mathtools}
\usepackage{geometry}

% 页面设置
\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}

% 数学环境设置
\newtheorem{definition}{定义}[section]
\newtheorem{theorem}{定理}[section]

% 自定义命令
\newcommand{\dd}{\mathrm{d}}
\newcommand{\ee}{\mathrm{e}}
\newcommand{\ii}{\mathrm{i}}

% 标题信息
\title{\textbf{tex2word 示例文档}}
\author{LaTeX to Word 转换示例}
\date{\today}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{引言}

这是一个展示 tex2word 转换功能的示例文档。该文档包含了各种 LaTeX 元素，包括数学公式、列表、表格等，用于测试转换器的功能。

\section{数学公式示例}

\subsection{内联数学公式}

在文本中可以包含内联数学公式，例如 $E = mc^2$，这是爱因斯坦的质能方程。我们也可以写更复杂的公式，如 $\int_{-\infty}^{\infty} \ee^{-x^2} \dd x = \sqrt{\pi}$。

希腊字母也很常用：$\alpha$, $\beta$, $\gamma$, $\delta$, $\epsilon$, $\theta$, $\lambda$, $\mu$, $\pi$, $\sigma$, $\phi$, $\omega$。

\subsection{显示数学公式}

下面是一些显示数学公式的例子：

\begin{equation}
\frac{\dd}{\dd x} \int_a^x f(t) \dd t = f(x)
\end{equation}

\begin{equation}
\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}
\end{equation}

\begin{equation}
\lim_{n \to \infty} \left(1 + \frac{1}{n}\right)^n = \ee
\end{equation}

\subsection{矩阵和方程组}

矩阵示例：
\begin{equation}
A = \begin{pmatrix}
a_{11} & a_{12} & a_{13} \\
a_{21} & a_{22} & a_{23} \\
a_{31} & a_{32} & a_{33}
\end{pmatrix}
\end{equation}

方程组示例：
\begin{equation}
\begin{cases}
x + y + z = 6 \\
2x - y + z = 3 \\
x + 2y - z = 1
\end{cases}
\end{equation}

\subsection{复杂数学表达式}

傅里叶变换：
\begin{equation}
F(\omega) = \int_{-\infty}^{\infty} f(t) \ee^{-\ii \omega t} \dd t
\end{equation}

泰勒级数：
\begin{equation}
f(x) = \sum_{n=0}^{\infty} \frac{f^{(n)}(a)}{n!} (x-a)^n
\end{equation}

偏微分方程：
\begin{equation}
\frac{\partial^2 u}{\partial t^2} = c^2 \nabla^2 u
\end{equation}

\section{列表示例}

\subsection{无序列表}

\begin{itemize}
\item 第一个项目
\item 第二个项目
\item 第三个项目，包含数学公式 $x^2 + y^2 = r^2$
\item 第四个项目
\end{itemize}

\subsection{有序列表}

\begin{enumerate}
\item 首先，我们定义函数 $f(x) = x^2$
\item 然后，计算导数 $f'(x) = 2x$
\item 接着，求积分 $\int f(x) \dd x = \frac{x^3}{3} + C$
\item 最后，验证结果
\end{enumerate}

\subsection{描述列表}

\begin{description}
\item[微积分] 研究函数的导数和积分
\item[线性代数] 研究向量空间和线性变换
\item[概率论] 研究随机现象的数学理论
\item[统计学] 研究数据收集、分析和解释的方法
\end{description}

\section{定理和定义}

\begin{definition}
设 $f: \mathbb{R} \to \mathbb{R}$ 是一个函数。如果对于任意 $\epsilon > 0$，存在 $\delta > 0$，使得当 $|x - a| < \delta$ 时，有 $|f(x) - f(a)| < \epsilon$，则称函数 $f$ 在点 $a$ 处连续。
\end{definition}

\begin{theorem}[中值定理]
设函数 $f$ 在闭区间 $[a, b]$ 上连续，在开区间 $(a, b)$ 内可导，则存在 $c \in (a, b)$，使得
\begin{equation}
f'(c) = \frac{f(b) - f(a)}{b - a}
\end{equation}
\end{theorem}

\section{文本格式示例}

这里展示各种文本格式：

\begin{itemize}
\item \textbf{粗体文本}
\item \textit{斜体文本}
\item \texttt{等宽字体文本}
\item \underline{下划线文本}
\item \emph{强调文本}
\end{itemize}

我们也可以在同一段落中混合使用不同的格式，例如：这是\textbf{粗体}，这是\textit{斜体}，这是\texttt{等宽字体}。

\section{特殊符号}

LaTeX 中的特殊符号：

数学符号：$\leq$, $\geq$, $\neq$, $\approx$, $\equiv$, $\propto$, $\infty$, $\partial$, $\nabla$

箭头：$\leftarrow$, $\rightarrow$, $\leftrightarrow$, $\Leftarrow$, $\Rightarrow$, $\Leftrightarrow$

集合符号：$\in$, $\notin$, $\subset$, $\supset$, $\cap$, $\cup$, $\emptyset$

\section{结论}

这个示例文档展示了 tex2word 转换器需要处理的各种 LaTeX 元素。通过这些测试用例，我们可以验证转换器是否能够正确地将 LaTeX 文档转换为 Word 格式，同时保持数学公式的优雅呈现和文档结构的完整性。

转换器应该能够：
\begin{enumerate}
\item 正确转换各种数学公式（内联和显示）
\item 保持文档的层次结构（章节、小节等）
\item 处理各种列表格式
\item 转换文本格式（粗体、斜体等）
\item 处理特殊符号和字符
\item 保持整体的排版美观
\end{enumerate}

\end{document}
