# tex2word: Professional LaTeX to Word Converter

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](tests/)

tex2word is a professional-grade Python package for converting LaTeX documents to Microsoft Word format while preserving mathematical formulas, document structure, and formatting. It features elegant math formula conversion from LaTeX to MathML, ensuring high-quality output suitable for academic and professional use.

## ✨ Key Features

- **🧮 Elegant Math Formula Conversion**: LaTeX math → MathML → Word equations
- **📄 Complete Document Structure Preservation**: Sections, subsections, lists, tables, figures
- **🎨 Configurable Style Mapping**: Customizable LaTeX to Word style conversion
- **⚡ Batch Processing**: Convert multiple files efficiently
- **🔧 Extensible Architecture**: Modular design for easy customization
- **📋 Comprehensive Testing**: Full test coverage for reliability
- **🖥️ User-Friendly CLI**: Command-line interface with rich output

## 🚀 Quick Start

### Installation

```bash
pip install tex2word
```

### Basic Usage

#### Command Line

```bash
# Convert a single file
tex2word convert document.tex document.docx

# Batch convert all .tex files in a directory
tex2word batch input_dir/ output_dir/

# Validate LaTeX without conversion
tex2word validate document.tex

# Generate sample configuration
tex2word config sample_config.yaml
```

#### Python API

```python
from tex2word import TexToWordConverter

# Basic conversion
converter = TexToWordConverter()
result = converter.convert('document.tex', 'document.docx')

# With custom configuration
config = {
    'math': {'conversion_method': 'mathml'},
    'styles': {'section': {'font_size': 14}}
}
converter = TexToWordConverter(custom_config=config)
result = converter.convert('document.tex', 'document.docx')

# Convert from string
latex_content = r"""
\documentclass{article}
\begin{document}
\section{Introduction}
This is a test with inline math $E = mc^2$ and display math:
\begin{equation}
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
\end{equation}
\end{document}
"""
result = converter.convert_string(latex_content, 'output.docx')
```

## 📖 Documentation

### Supported LaTeX Elements

#### Document Structure
- `\documentclass`, `\title`, `\author`, `\date`
- `\section`, `\subsection`, `\subsubsection`
- `\maketitle`, `\tableofcontents`
- `\abstract`

#### Mathematical Formulas
- Inline math: `$...$`, `\(...\)`
- Display math: `$$...$$`, `\[...\]`
- Equation environments: `equation`, `align`, `gather`
- Matrix environments: `matrix`, `pmatrix`, `bmatrix`
- Mathematical symbols and operators
- Custom commands: `\dd`, `\ee`, `\ii`, `\jj`

#### Lists and Tables
- `itemize`, `enumerate`, `description`
- `table`, `tabular`, `longtable`
- `figure` environments

#### Text Formatting
- `\textbf`, `\textit`, `\texttt`
- `\emph`, `\underline`
- Special characters and symbols

### Configuration

tex2word uses YAML configuration files for customization:

```yaml
# Sample configuration
conversion:
  preserve_comments: false
  convert_references: true
  strict_mode: false

math:
  conversion_method: mathml  # mathml, unicode, image
  display_numbering: true
  fallback_to_text: true
  custom_commands:
    dd: \mathrm{d}
    ee: \mathrm{e}

document:
  page_size: A4
  font_family: Times New Roman
  font_size: 11

styles:
  section:
    font_size: 14
    bold: true
    numbering: true
  equation:
    alignment: center
    numbering: true
```

### Advanced Usage

#### Custom Math Commands

```python
# Define custom LaTeX commands
config = {
    'math': {
        'custom_commands': {
            'dd': r'\mathrm{d}',
            'ee': r'\mathrm{e}',
            'Real': r'\mathbb{R}',
            'Complex': r'\mathbb{C}'
        }
    }
}

converter = TexToWordConverter(custom_config=config)
```

#### Batch Processing with Custom Options

```python
from pathlib import Path

converter = TexToWordConverter()
input_dir = Path('latex_files')
output_dir = Path('word_files')

for tex_file in input_dir.glob('*.tex'):
    output_file = output_dir / f"{tex_file.stem}.docx"
    try:
        result = converter.convert(str(tex_file), str(output_file))
        print(f"✓ Converted {tex_file.name}")
    except Exception as e:
        print(f"✗ Failed to convert {tex_file.name}: {e}")
```

#### Validation and Error Handling

```python
# Validate LaTeX before conversion
validation_result = converter.validate_latex(latex_content)

if validation_result['valid']:
    print("LaTeX is valid")
    print(f"Statistics: {validation_result['statistics']}")
else:
    print("LaTeX has errors:")
    for error in validation_result['errors']:
        print(f"  - {error}")
```

## 🏗️ Architecture

tex2word follows a modular architecture with clear separation of concerns:

```
tex2word/
├── core/                    # Core functionality
│   ├── converter.py        # Main converter class
│   ├── parser.py           # Base parser interface
│   └── exceptions.py       # Custom exceptions
├── parsers/                # Specialized parsers
│   ├── document_parser.py  # Document structure
│   ├── math_parser.py      # Mathematical formulas
│   └── content_parser.py   # General content
├── converters/             # Conversion engines
│   ├── math_converter.py   # Math formula conversion
│   └── structure_converter.py # Document structure
├── generators/             # Output generators
│   └── docx_generator.py   # Word document generation
├── config/                 # Configuration management
│   └── settings.py         # Configuration system
└── utils/                  # Utility functions
    └── helpers.py          # Helper functions
```

## 🧪 Testing

Run the test suite:

```bash
# Install development dependencies
pip install -e .[dev]

# Run all tests
pytest

# Run with coverage
pytest --cov=tex2word

# Run specific test categories
pytest tests/test_math_converter.py  # Math conversion tests
pytest tests/test_converter.py       # Integration tests
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/tex2word/tex2word.git
cd tex2word

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e .[dev]

# Run tests
pytest
```

## 📋 Requirements

- Python 3.8+
- python-docx >= 0.8.11
- lxml >= 4.9.0
- latex2mathml >= 3.76.0
- PyYAML >= 6.0
- click >= 8.1.0
- rich >= 13.0.0 (optional, for enhanced CLI)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [python-docx](https://python-docx.readthedocs.io/) for Word document generation
- [latex2mathml](https://github.com/roniemartinez/latex2mathml) for LaTeX to MathML conversion
- [rich](https://github.com/Textualize/rich) for beautiful terminal output

## 📞 Support

- 📖 [Documentation](https://tex2word.readthedocs.io/)
- 🐛 [Issue Tracker](https://github.com/tex2word/tex2word/issues)
- 💬 [Discussions](https://github.com/tex2word/tex2word/discussions)

---

**tex2word** - Converting LaTeX to Word with mathematical elegance. ✨
