"""
Configuration management for tex2word package.

This module provides comprehensive configuration management for customizing
all aspects of the LaTeX to Word conversion process.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from copy import deepcopy

from ..core.exceptions import ConfigurationError


# Default configuration
DEFAULT_CONFIG = {
    'conversion': {
        'preserve_comments': False,
        'convert_references': True,
        'handle_citations': True,
        'process_includes': True,
        'strict_mode': False
    },
    
    'math': {
        'conversion_method': 'mathml',  # 'mathml', 'unicode', 'image'
        'inline_delimiter': 'auto',    # 'auto', 'parentheses', 'brackets'
        'display_numbering': True,
        'equation_spacing': 'auto',
        'fallback_to_text': True,
        'custom_commands': {
            'dd': r'\mathrm{d}',
            'ee': r'\mathrm{e}',
            'ii': r'\mathrm{i}',
            'jj': r'\mathrm{j}'
        }
    },
    
    'document': {
        'page_size': 'A4',
        'margins': {
            'top': '2.5cm',
            'bottom': '2.5cm',
            'left': '2.5cm',
            'right': '2.5cm'
        },
        'line_spacing': 1.15,
        'font_family': 'Times New Roman',
        'font_size': 11
    },
    
    'styles': {
        'title': {
            'font_name': 'Times New Roman',
            'font_size': 16,
            'bold': True,
            'space_after': '12pt'
        },
        'author': {
            'font_name': 'Times New Roman',
            'font_size': 12,
            'space_after': '6pt'
        },
        'section': {
            'font_name': 'Times New Roman',
            'font_size': 14,
            'bold': True,
            'space_before': '12pt',
            'space_after': '6pt',
            'numbering': True
        },
        'subsection': {
            'font_name': 'Times New Roman',
            'font_size': 12,
            'bold': True,
            'space_before': '10pt',
            'space_after': '4pt',
            'numbering': True
        },
        'normal': {
            'font_name': 'Times New Roman',
            'font_size': 11,
            'line_spacing': 1.15,
            'space_after': '6pt',
            'justify': True
        },
        'equation': {
            'alignment': 'center',
            'space_before': '6pt',
            'space_after': '6pt',
            'numbering': True
        },
        'list': {
            'indent': '0.5in',
            'space_after': '3pt'
        },
        'table': {
            'alignment': 'center',
            'space_before': '6pt',
            'space_after': '6pt',
            'border_style': 'single'
        }
    },
    
    'output': {
        'format': 'docx',
        'compatibility_mode': 'word2016',
        'embed_fonts': False,
        'compress_images': True,
        'image_quality': 'high'
    },
    
    'logging': {
        'level': 'INFO',
        'log_file': None,
        'verbose': False
    }
}


class ConfigManager:
    """Manager for tex2word configuration."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file (YAML format)
        """
        self.config = deepcopy(DEFAULT_CONFIG)
        self.config_path = config_path
        
        if config_path:
            self.load_config(config_path)
    
    def load_config(self, config_path: str) -> None:
        """
        Load configuration from file.
        
        Args:
            config_path: Path to configuration file
            
        Raises:
            ConfigurationError: If configuration file is invalid
        """
        try:
            config_file = Path(config_path)
            
            if not config_file.exists():
                raise ConfigurationError(
                    f"Configuration file not found: {config_path}",
                    config_key="file_path",
                    config_value=config_path
                )
            
            with open(config_file, 'r', encoding='utf-8') as file:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    user_config = yaml.safe_load(file)
                else:
                    raise ConfigurationError(
                        f"Unsupported configuration file format: {config_path}",
                        config_key="file_format"
                    )
            
            if user_config:
                self._merge_config(user_config)
                self._validate_config()
            
        except yaml.YAMLError as e:
            raise ConfigurationError(
                f"Invalid YAML configuration: {str(e)}",
                config_key="yaml_syntax"
            ) from e
        except Exception as e:
            raise ConfigurationError(
                f"Failed to load configuration: {str(e)}",
                config_key="file_loading"
            ) from e
    
    def save_config(self, config_path: str) -> None:
        """
        Save current configuration to file.
        
        Args:
            config_path: Path to save configuration file
        """
        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                
        except Exception as e:
            raise ConfigurationError(
                f"Failed to save configuration: {str(e)}",
                config_key="file_saving"
            ) from e
    
    def _merge_config(self, user_config: Dict[str, Any]) -> None:
        """
        Merge user configuration with default configuration.
        
        Args:
            user_config: User configuration dictionary
        """
        def merge_dict(base: Dict, update: Dict) -> Dict:
            """Recursively merge dictionaries."""
            result = base.copy()
            for key, value in update.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self.config = merge_dict(self.config, user_config)
    
    def _validate_config(self) -> None:
        """
        Validate configuration values.
        
        Raises:
            ConfigurationError: If configuration is invalid
        """
        # Validate math conversion method
        valid_math_methods = ['mathml', 'unicode', 'image']
        math_method = self.config.get('math', {}).get('conversion_method')
        if math_method not in valid_math_methods:
            raise ConfigurationError(
                f"Invalid math conversion method: {math_method}",
                config_key="math.conversion_method",
                config_value=math_method
            )
        
        # Validate page size
        valid_page_sizes = ['A4', 'Letter', 'Legal', 'A3', 'A5']
        page_size = self.config.get('document', {}).get('page_size')
        if page_size not in valid_page_sizes:
            raise ConfigurationError(
                f"Invalid page size: {page_size}",
                config_key="document.page_size",
                config_value=page_size
            )
        
        # Validate output format
        valid_formats = ['docx']
        output_format = self.config.get('output', {}).get('format')
        if output_format not in valid_formats:
            raise ConfigurationError(
                f"Invalid output format: {output_format}",
                config_key="output.format",
                config_value=output_format
            )
        
        # Validate logging level
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        log_level = self.config.get('logging', {}).get('level')
        if log_level not in valid_log_levels:
            raise ConfigurationError(
                f"Invalid logging level: {log_level}",
                config_key="logging.level",
                config_value=log_level
            )
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Configuration dictionary
        """
        return deepcopy(self.config)
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary with configuration updates
        """
        self._merge_config(updates)
        self._validate_config()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get specific configuration section.
        
        Args:
            section: Section name (e.g., 'math', 'styles')
            
        Returns:
            Configuration section dictionary
        """
        return deepcopy(self.config.get(section, {}))
    
    def set_section(self, section: str, config: Dict[str, Any]) -> None:
        """
        Set configuration for specific section.
        
        Args:
            section: Section name
            config: Configuration dictionary for the section
        """
        self.config[section] = deepcopy(config)
        self._validate_config()
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated key path (e.g., 'math.conversion_method')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_value(self, key_path: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key_path: Dot-separated key path
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to parent dictionary
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        self._validate_config()
    
    def reset_to_defaults(self) -> None:
        """Reset configuration to default values."""
        self.config = deepcopy(DEFAULT_CONFIG)
    
    def create_sample_config(self, output_path: str) -> None:
        """
        Create a sample configuration file.
        
        Args:
            output_path: Path to save sample configuration
        """
        sample_config = {
            '# tex2word Configuration File': None,
            '# This file contains configuration options for LaTeX to Word conversion': None,
            '': None,
            'conversion': {
                'preserve_comments': False,
                'convert_references': True,
                'strict_mode': False
            },
            'math': {
                'conversion_method': 'mathml',
                'display_numbering': True,
                'fallback_to_text': True
            },
            'document': {
                'page_size': 'A4',
                'font_family': 'Times New Roman',
                'font_size': 11
            },
            'styles': {
                'section': {
                    'font_size': 14,
                    'bold': True,
                    'numbering': True
                }
            }
        }
        
        try:
            with open(output_path, 'w', encoding='utf-8') as file:
                yaml.dump(sample_config, file, default_flow_style=False,
                         allow_unicode=True, indent=2)
        except Exception as e:
            raise ConfigurationError(
                f"Failed to create sample configuration: {str(e)}",
                config_key="sample_creation"
            ) from e
