#!/usr/bin/env python3
"""
Demo script for tex2word package.

This script demonstrates various features of the tex2word converter,
including basic conversion, custom configuration, and batch processing.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import tex2word
sys.path.insert(0, str(Path(__file__).parent.parent))

from tex2word import TexToWordConverter
from tex2word.core.exceptions import TexToWordError


def demo_basic_conversion():
    """Demonstrate basic LaTeX to Word conversion."""
    print("🔄 Demo 1: Basic Conversion")
    print("=" * 50)
    
    # Simple LaTeX content
    latex_content = r"""
    \documentclass{article}
    \title{Demo Document}
    \author{tex2word Demo}
    \begin{document}
    \maketitle
    
    \section{Introduction}
    This is a simple demonstration of tex2word conversion capabilities.
    
    \subsection{Mathematics}
    Here's some inline math: $E = mc^2$.
    
    And here's a display equation:
    \begin{equation}
    \int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
    \end{equation}
    
    \subsection{Lists}
    \begin{itemize}
    \item First item
    \item Second item with math: $x^2 + y^2 = r^2$
    \item Third item
    \end{itemize}
    
    \section{Conclusion}
    This demonstrates the basic conversion functionality.
    \end{document}
    """
    
    try:
        # Create converter
        converter = TexToWordConverter()
        
        # Convert to Word
        output_path = "demo_basic.docx"
        result = converter.convert_string(latex_content, output_path)
        
        if result['success']:
            print(f"✅ Conversion successful!")
            print(f"📄 Output file: {output_path}")
            print(f"📊 Statistics:")
            stats = result['statistics']
            for key, value in stats.items():
                print(f"   {key}: {value}")
            
            if result['warnings']:
                print(f"⚠️  Warnings:")
                for warning in result['warnings']:
                    print(f"   - {warning}")
        else:
            print("❌ Conversion failed")
            
    except TexToWordError as e:
        print(f"❌ Conversion error: {e.message}")
        if e.details:
            print(f"   Details: {e.details}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    print()


def demo_custom_configuration():
    """Demonstrate conversion with custom configuration."""
    print("🔄 Demo 2: Custom Configuration")
    print("=" * 50)
    
    # Custom configuration
    custom_config = {
        'math': {
            'conversion_method': 'mathml',
            'display_numbering': True,
            'custom_commands': {
                'Real': r'\mathbb{R}',
                'Complex': r'\mathbb{C}',
                'dd': r'\mathrm{d}'
            }
        },
        'styles': {
            'section': {
                'font_size': 16,
                'bold': True,
                'numbering': True
            },
            'normal': {
                'font_size': 12,
                'line_spacing': 1.2
            }
        },
        'document': {
            'font_family': 'Arial',
            'page_size': 'A4'
        }
    }
    
    latex_content = r"""
    \documentclass{article}
    \newcommand{\Real}{\mathbb{R}}
    \newcommand{\Complex}{\mathbb{C}}
    \newcommand{\dd}{\mathrm{d}}
    
    \title{Advanced Mathematics Demo}
    \author{tex2word Advanced Demo}
    
    \begin{document}
    \maketitle
    
    \section{Real Analysis}
    Let $f: \Real \to \Real$ be a function. The derivative is defined as:
    \begin{equation}
    f'(x) = \lim_{h \to 0} \frac{f(x+h) - f(x)}{h}
    \end{equation}
    
    \section{Complex Analysis}
    For $z \in \Complex$, the complex exponential is:
    \begin{equation}
    e^z = \sum_{n=0}^{\infty} \frac{z^n}{n!}
    \end{equation}
    
    \section{Integration}
    The fundamental theorem of calculus states:
    \begin{equation}
    \int_a^b f'(x) \dd x = f(b) - f(a)
    \end{equation}
    \end{document}
    """
    
    try:
        # Create converter with custom configuration
        converter = TexToWordConverter(custom_config=custom_config)
        
        # Convert to Word
        output_path = "demo_custom.docx"
        result = converter.convert_string(latex_content, output_path)
        
        if result['success']:
            print(f"✅ Custom conversion successful!")
            print(f"📄 Output file: {output_path}")
            print(f"⚙️  Applied custom configuration:")
            print(f"   - Math method: {custom_config['math']['conversion_method']}")
            print(f"   - Font family: {custom_config['document']['font_family']}")
            print(f"   - Section font size: {custom_config['styles']['section']['font_size']}")
        else:
            print("❌ Conversion failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def demo_validation():
    """Demonstrate LaTeX validation."""
    print("🔄 Demo 3: LaTeX Validation")
    print("=" * 50)
    
    # Valid LaTeX
    valid_latex = r"""
    \documentclass{article}
    \begin{document}
    \section{Test}
    This is valid LaTeX with $x = y$.
    \end{document}
    """
    
    # Invalid LaTeX
    invalid_latex = r"""
    \documentclass{article}
    \begin{document}
    \section{Test
    This has unmatched braces { and missing math delimiters $x = y
    \end{document}
    """
    
    converter = TexToWordConverter()
    
    print("📝 Validating valid LaTeX:")
    try:
        result = converter.validate_latex(valid_latex)
        if result['valid']:
            print("✅ LaTeX is valid")
            print(f"📊 Statistics: {result['statistics']}")
        else:
            print("❌ LaTeX is invalid")
            for error in result['errors']:
                print(f"   Error: {error}")
    except Exception as e:
        print(f"❌ Validation error: {e}")
    
    print("\n📝 Validating invalid LaTeX:")
    try:
        result = converter.validate_latex(invalid_latex)
        if result['valid']:
            print("✅ LaTeX is valid")
        else:
            print("❌ LaTeX is invalid (as expected)")
            for error in result['errors']:
                print(f"   Error: {error}")
            for warning in result['warnings']:
                print(f"   Warning: {warning}")
    except Exception as e:
        print(f"❌ Validation error: {e}")
    
    print()


def demo_file_conversion():
    """Demonstrate file-based conversion."""
    print("🔄 Demo 4: File Conversion")
    print("=" * 50)
    
    # Check if sample document exists
    sample_file = Path("examples/sample_document.tex")
    if not sample_file.exists():
        print(f"❌ Sample file not found: {sample_file}")
        print("   Please run this demo from the project root directory.")
        return
    
    try:
        converter = TexToWordConverter()
        
        # Convert the sample document
        output_path = "demo_sample.docx"
        print(f"📄 Converting {sample_file} to {output_path}...")
        
        result = converter.convert(str(sample_file), output_path)
        
        if result['success']:
            print(f"✅ File conversion successful!")
            print(f"📄 Input: {result['input_file']}")
            print(f"📄 Output: {result['output_file']}")
            print(f"📊 Statistics:")
            stats = result['statistics']
            for key, value in stats.items():
                print(f"   {key}: {value}")
        else:
            print("❌ File conversion failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print()


def demo_error_handling():
    """Demonstrate error handling."""
    print("🔄 Demo 5: Error Handling")
    print("=" * 50)
    
    # LaTeX with problematic content
    problematic_latex = r"""
    \documentclass{article}
    \begin{document}
    \section{Test}
    This has some issues: \unknowncommand{test}
    And unmatched math: $x = y + z
    \end{document}
    """
    
    try:
        converter = TexToWordConverter()
        
        # Try to convert problematic content
        output_path = "demo_error.docx"
        result = converter.convert_string(problematic_latex, output_path)
        
        print(f"📊 Conversion completed with warnings:")
        print(f"   Success: {result['success']}")
        print(f"   Warnings: {len(result.get('warnings', []))}")
        
        for warning in result.get('warnings', []):
            print(f"   ⚠️  {warning}")
            
    except TexToWordError as e:
        print(f"❌ tex2word error: {e.message}")
        if e.details:
            print(f"   Details: {e.details}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    print()


def main():
    """Run all demos."""
    print("🚀 tex2word Demo Script")
    print("=" * 50)
    print("This script demonstrates the capabilities of tex2word converter.")
    print()
    
    # Run all demos
    demo_basic_conversion()
    demo_custom_configuration()
    demo_validation()
    demo_file_conversion()
    demo_error_handling()
    
    print("🎉 Demo completed!")
    print("\nGenerated files:")
    for file in ["demo_basic.docx", "demo_custom.docx", "demo_sample.docx", "demo_error.docx"]:
        if os.path.exists(file):
            print(f"   📄 {file}")
    
    print("\n💡 Try the command-line interface:")
    print("   tex2word convert examples/sample_document.tex output.docx")
    print("   tex2word validate examples/sample_document.tex")
    print("   tex2word config sample_config.yaml")


if __name__ == "__main__":
    main()
