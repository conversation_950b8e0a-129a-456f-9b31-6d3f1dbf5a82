"""
Helper functions for tex2word package.

This module provides various utility functions used throughout
the tex2word package for common operations.
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any


def clean_latex_content(content: str) -> str:
    """
    Clean LaTeX content by removing comments and normalizing whitespace.
    
    Args:
        content: Raw LaTeX content
        
    Returns:
        Cleaned LaTeX content
    """
    # Remove comments (lines starting with %)
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Find comment position (not escaped %)
        comment_pos = -1
        i = 0
        while i < len(line):
            if line[i] == '%' and (i == 0 or line[i-1] != '\\'):
                comment_pos = i
                break
            i += 1
        
        if comment_pos >= 0:
            line = line[:comment_pos]
        
        cleaned_lines.append(line.rstrip())
    
    # Join lines and normalize whitespace
    cleaned = '\n'.join(cleaned_lines)
    return normalize_whitespace(cleaned)


def normalize_whitespace(text: str) -> str:
    """
    Normalize whitespace in text.
    
    Args:
        text: Text to normalize
        
    Returns:
        Text with normalized whitespace
    """
    # Replace multiple spaces with single space
    text = re.sub(r' +', ' ', text)
    
    # Replace multiple newlines with double newline
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    
    # Remove trailing whitespace from lines
    lines = text.split('\n')
    lines = [line.rstrip() for line in lines]
    
    return '\n'.join(lines)


def extract_document_metadata(content: str) -> Dict[str, Optional[str]]:
    """
    Extract document metadata from LaTeX content.
    
    Args:
        content: LaTeX content
        
    Returns:
        Dictionary with metadata (title, author, date, etc.)
    """
    metadata = {
        'title': None,
        'author': None,
        'date': None,
        'documentclass': None,
        'abstract': None
    }
    
    # Extract title
    title_match = re.search(r'\\title\s*\{([^}]*)\}', content)
    if title_match:
        metadata['title'] = title_match.group(1).strip()
    
    # Extract author
    author_match = re.search(r'\\author\s*\{([^}]*)\}', content)
    if author_match:
        metadata['author'] = author_match.group(1).strip()
    
    # Extract date
    date_match = re.search(r'\\date\s*\{([^}]*)\}', content)
    if date_match:
        metadata['date'] = date_match.group(1).strip()
    
    # Extract document class
    docclass_match = re.search(r'\\documentclass(?:\[[^\]]*\])?\s*\{([^}]*)\}', content)
    if docclass_match:
        metadata['documentclass'] = docclass_match.group(1).strip()
    
    # Extract abstract
    abstract_match = re.search(r'\\begin\{abstract\}(.*?)\\end\{abstract\}', content, re.DOTALL)
    if abstract_match:
        metadata['abstract'] = abstract_match.group(1).strip()
    
    return metadata


def validate_file_path(file_path: str, must_exist: bool = True, 
                      allowed_extensions: Optional[List[str]] = None) -> bool:
    """
    Validate file path.
    
    Args:
        file_path: Path to validate
        must_exist: Whether file must exist
        allowed_extensions: List of allowed file extensions
        
    Returns:
        True if valid, False otherwise
    """
    path = Path(file_path)
    
    # Check if file exists (if required)
    if must_exist and not path.exists():
        return False
    
    # Check file extension
    if allowed_extensions:
        if path.suffix.lower() not in [ext.lower() for ext in allowed_extensions]:
            return False
    
    # Check if path is valid
    try:
        path.resolve()
        return True
    except (OSError, ValueError):
        return False


def ensure_directory_exists(directory_path: str) -> bool:
    """
    Ensure directory exists, create if necessary.
    
    Args:
        directory_path: Path to directory
        
    Returns:
        True if directory exists or was created successfully
    """
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except (OSError, PermissionError):
        return False


def split_latex_content(content: str) -> Dict[str, str]:
    """
    Split LaTeX content into preamble and document body.
    
    Args:
        content: LaTeX content
        
    Returns:
        Dictionary with 'preamble' and 'body' keys
    """
    # Find document environment
    begin_match = re.search(r'\\begin\{document\}', content)
    end_match = re.search(r'\\end\{document\}', content)
    
    if begin_match and end_match:
        preamble = content[:begin_match.start()].strip()
        body_start = begin_match.end()
        body_end = end_match.start()
        body = content[body_start:body_end].strip()
    else:
        # No document environment found
        preamble = ""
        body = content
    
    return {
        'preamble': preamble,
        'body': body
    }


def extract_packages(content: str) -> List[Dict[str, Any]]:
    """
    Extract package information from LaTeX content.
    
    Args:
        content: LaTeX content
        
    Returns:
        List of package dictionaries with name and options
    """
    packages = []
    
    # Find all \usepackage commands
    package_pattern = r'\\usepackage(?:\[([^\]]*)\])?\s*\{([^}]*)\}'
    matches = re.finditer(package_pattern, content)
    
    for match in matches:
        options = match.group(1) if match.group(1) else None
        package_names = match.group(2).split(',')
        
        for package_name in package_names:
            packages.append({
                'name': package_name.strip(),
                'options': options.split(',') if options else []
            })
    
    return packages


def find_latex_environments(content: str) -> List[Dict[str, Any]]:
    """
    Find all LaTeX environments in content.
    
    Args:
        content: LaTeX content
        
    Returns:
        List of environment dictionaries
    """
    environments = []
    
    # Pattern to match \begin{env}...\end{env}
    env_pattern = r'\\begin\{([^}]+)\}(.*?)\\end\{\1\}'
    matches = re.finditer(env_pattern, content, re.DOTALL)
    
    for match in matches:
        env_name = match.group(1)
        env_content = match.group(2)
        start_pos = match.start()
        end_pos = match.end()
        
        environments.append({
            'name': env_name,
            'content': env_content,
            'start_pos': start_pos,
            'end_pos': end_pos,
            'full_match': match.group(0)
        })
    
    return environments


def count_latex_elements(content: str) -> Dict[str, int]:
    """
    Count various LaTeX elements in content.
    
    Args:
        content: LaTeX content
        
    Returns:
        Dictionary with element counts
    """
    counts = {
        'sections': 0,
        'subsections': 0,
        'subsubsections': 0,
        'equations': 0,
        'inline_math': 0,
        'figures': 0,
        'tables': 0,
        'lists': 0,
        'references': 0,
        'citations': 0
    }
    
    # Count sections
    counts['sections'] = len(re.findall(r'\\section\*?\s*\{', content))
    counts['subsections'] = len(re.findall(r'\\subsection\*?\s*\{', content))
    counts['subsubsections'] = len(re.findall(r'\\subsubsection\*?\s*\{', content))
    
    # Count math
    counts['equations'] = len(re.findall(r'\\begin\{equation\*?\}', content))
    counts['inline_math'] = len(re.findall(r'\$[^$]+\$', content))
    
    # Count figures and tables
    counts['figures'] = len(re.findall(r'\\begin\{figure\*?\}', content))
    counts['tables'] = len(re.findall(r'\\begin\{table\*?\}', content))
    
    # Count lists
    list_envs = ['itemize', 'enumerate', 'description']
    for env in list_envs:
        counts['lists'] += len(re.findall(rf'\\begin\{{{env}\}}', content))
    
    # Count references and citations
    counts['references'] = len(re.findall(r'\\ref\s*\{[^}]+\}', content))
    counts['citations'] = len(re.findall(r'\\cite\s*\{[^}]+\}', content))
    
    return counts


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a file.
    
    Args:
        file_path: Path to file
        
    Returns:
        Dictionary with file information
    """
    path = Path(file_path)
    
    if not path.exists():
        return {'exists': False}
    
    stat = path.stat()
    
    return {
        'exists': True,
        'size': stat.st_size,
        'size_formatted': format_file_size(stat.st_size),
        'modified': stat.st_mtime,
        'extension': path.suffix,
        'name': path.name,
        'stem': path.stem
    }
