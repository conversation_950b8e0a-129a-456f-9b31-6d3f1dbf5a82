"""
Tests for the math converter module.

This module contains comprehensive tests for the mathematical formula
conversion functionality, ensuring accurate LaTeX to MathML conversion.
"""

import pytest
from unittest.mock import Mock, patch
import xml.etree.ElementTree as ET

from tex2word.core.parser import ParsedElement
from tex2word.converters.math_converter import Math<PERSON>onverter, MathMLElement
from tex2word.core.exceptions import MathConversionError


class TestMathConverter:
    """Test cases for MathConverter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = MathConverter()
    
    def test_initialization(self):
        """Test converter initialization."""
        assert self.converter.equation_counter == 0
        assert isinstance(self.converter.reference_map, dict)
        assert len(self.converter.reference_map) == 0
    
    def test_symbol_mappings(self):
        """Test LaTeX symbol mappings."""
        # Test Greek letters
        assert r'\alpha' in self.converter.SYMBOL_MAPPINGS
        assert self.converter.SYMBOL_MAPPINGS[r'\alpha'] == 'α'
        
        # Test mathematical operators
        assert r'\pm' in self.converter.SYMBOL_MAPPINGS
        assert self.converter.SYMBOL_MAPPINGS[r'\pm'] == '±'
        
        # Test relations
        assert r'\leq' in self.converter.SYMBOL_MAPPINGS
        assert self.converter.SYMBOL_MAPPINGS[r'\leq'] == '≤'
    
    def test_preprocess_latex_basic(self):
        """Test basic LaTeX preprocessing."""
        # Test whitespace normalization
        latex = "x   +    y"
        processed = self.converter._preprocess_latex(latex)
        assert processed == "x + y"
        
        # Test custom command expansion
        latex = r"\dd x"
        processed = self.converter._preprocess_latex(latex)
        assert r"\mathrm{d}" in processed
    
    def test_preprocess_latex_symbols(self):
        """Test symbol replacement in preprocessing."""
        latex = r"\alpha + \beta = \gamma"
        processed = self.converter._preprocess_latex(latex)
        assert "α" in processed
        assert "β" in processed
        assert "γ" in processed
    
    def test_expand_custom_commands(self):
        """Test custom command expansion."""
        content = r"\dd x + \ee^x + \ii \omega"
        expanded = self.converter._expand_custom_commands(content)
        
        assert r"\mathrm{d}" in expanded
        assert r"\mathrm{e}" in expanded
        assert r"\mathrm{i}" in expanded
    
    def test_normalize_delimiters(self):
        """Test delimiter normalization."""
        content = r"\left( x + y \right)"
        normalized = self.converter._normalize_delimiters(content)
        assert r"\left" not in normalized
        assert r"\right" not in normalized
        assert "( x + y )" in normalized
    
    def test_normalize_matrices(self):
        """Test matrix normalization."""
        content = r"\begin{pmatrix} a & b \\ c & d \end{pmatrix}"
        normalized = self.converter._normalize_matrices(content)
        assert r"\begin{matrix}" in normalized
        assert r"\end{matrix}" in normalized
    
    def test_normalize_cases(self):
        """Test cases environment normalization."""
        content = r"\begin{cases} x & \text{if } x > 0 \\ 0 & \text{otherwise} \end{cases}"
        normalized = self.converter._normalize_cases(content)
        assert r"\begin{cases}" in normalized
        assert r"\end{cases}" in normalized
        assert " & " in normalized
    
    @patch('tex2word.converters.math_converter.latex_to_mathml')
    def test_convert_to_mathml_success(self, mock_latex_to_mathml):
        """Test successful MathML conversion."""
        mock_latex_to_mathml.return_value = '<math><mi>x</mi></math>'
        
        result = self.converter._convert_to_mathml("x")
        assert result == '<math><mi>x</mi></math>'
        mock_latex_to_mathml.assert_called_once_with("x")
    
    @patch('tex2word.converters.math_converter.latex_to_mathml')
    def test_convert_to_mathml_fallback(self, mock_latex_to_mathml):
        """Test MathML conversion with fallback."""
        # First call fails, second call succeeds
        mock_latex_to_mathml.side_effect = [Exception("Conversion failed"), '<math><mi>x</mi></math>']
        
        result = self.converter._convert_to_mathml("complex_formula")
        assert '<math><mi>x</mi></math>' in result
        assert mock_latex_to_mathml.call_count == 2
    
    @patch('tex2word.converters.math_converter.latex_to_mathml')
    def test_convert_to_mathml_complete_fallback(self, mock_latex_to_mathml):
        """Test MathML conversion with complete fallback."""
        mock_latex_to_mathml.side_effect = Exception("Conversion failed")
        
        result = self.converter._convert_to_mathml("unsupported_formula")
        assert '<math' in result
        assert 'unsupported_formula' in result
    
    def test_postprocess_mathml_basic(self):
        """Test basic MathML post-processing."""
        mathml = '<math><mi>x</mi></math>'
        processed = self.converter._postprocess_mathml(mathml, 'inline')
        
        # Should add namespace
        assert 'xmlns="http://www.w3.org/1998/Math/MathML"' in processed
    
    def test_postprocess_mathml_display(self):
        """Test MathML post-processing for display math."""
        mathml = '<math><mi>x</mi></math>'
        processed = self.converter._postprocess_mathml(mathml, 'display')
        
        # Should add display attribute
        assert 'display="block"' in processed
    
    def test_postprocess_mathml_invalid(self):
        """Test MathML post-processing with invalid XML."""
        invalid_mathml = '<math><mi>x</mi>'  # Missing closing tag
        processed = self.converter._postprocess_mathml(invalid_mathml, 'inline')
        
        # Should wrap in basic structure
        assert '<math xmlns="http://www.w3.org/1998/Math/MathML">' in processed
    
    def test_convert_math_element_inline(self):
        """Test conversion of inline math element."""
        element = ParsedElement(
            element_type='math',
            content='x + y',
            attributes={'math_type': 'inline'},
            start_pos=0,
            end_pos=5,
            line_number=1
        )
        
        with patch.object(self.converter, '_convert_to_mathml') as mock_convert:
            mock_convert.return_value = '<math><mi>x</mi><mo>+</mo><mi>y</mi></math>'
            
            result = self.converter.convert_math_element(element)
            
            assert isinstance(result, MathMLElement)
            assert result.display_type == 'inline'
            assert result.original_latex == 'x + y'
            assert 'math_0' in result.element_id
    
    def test_convert_math_element_display(self):
        """Test conversion of display math element."""
        element = ParsedElement(
            element_type='math',
            content='E = mc^2',
            attributes={'math_type': 'display'},
            start_pos=0,
            end_pos=8,
            line_number=1
        )
        
        with patch.object(self.converter, '_convert_to_mathml') as mock_convert:
            mock_convert.return_value = '<math><mi>E</mi><mo>=</mo><mi>m</mi><msup><mi>c</mi><mn>2</mn></msup></math>'
            
            result = self.converter.convert_math_element(element)
            
            assert isinstance(result, MathMLElement)
            assert result.display_type == 'block'
            assert result.original_latex == 'E = mc^2'
    
    def test_convert_math_element_error(self):
        """Test math element conversion error handling."""
        element = ParsedElement(
            element_type='math',
            content='invalid_formula',
            attributes={'math_type': 'inline'},
            start_pos=0,
            end_pos=15,
            line_number=1
        )
        
        with patch.object(self.converter, '_convert_to_mathml') as mock_convert:
            mock_convert.side_effect = Exception("Conversion failed")
            
            with pytest.raises(MathConversionError) as exc_info:
                self.converter.convert_math_element(element)
            
            assert "Failed to convert math formula" in str(exc_info.value)
            assert exc_info.value.latex_formula == 'invalid_formula'
            assert exc_info.value.formula_type == 'inline'
    
    def test_convert_equation_reference(self):
        """Test equation reference conversion."""
        # First reference
        ref1 = self.converter.convert_equation_reference('eq:test1')
        assert ref1 == '1'
        
        # Second reference
        ref2 = self.converter.convert_equation_reference('eq:test2')
        assert ref2 == '2'
        
        # Repeat first reference
        ref1_repeat = self.converter.convert_equation_reference('eq:test1')
        assert ref1_repeat == '1'
    
    def test_get_conversion_statistics(self):
        """Test conversion statistics."""
        # Convert some elements to generate statistics
        self.converter.equation_counter = 5
        self.converter.reference_map = {'eq:1': '1', 'eq:2': '2'}
        
        stats = self.converter.get_conversion_statistics()
        
        assert stats['total_equations'] == 5
        assert stats['total_references'] == 2
    
    def test_simplify_for_fallback(self):
        """Test LaTeX simplification for fallback."""
        complex_latex = r"x &= y \\ z &= w \hspace{1cm} test"
        simplified = self.converter._simplify_for_fallback(complex_latex)
        
        assert '&' not in simplified
        assert r'\\' not in simplified
        assert r'\hspace' not in simplified
    
    def test_create_fallback_mathml(self):
        """Test fallback MathML creation."""
        latex = "unsupported_formula"
        fallback = self.converter._create_fallback_mathml(latex)
        
        assert '<math' in fallback
        assert 'xmlns="http://www.w3.org/1998/Math/MathML"' in fallback
        assert 'unsupported_formula' in fallback
    
    def test_optimize_mathml_structure(self):
        """Test MathML structure optimization."""
        # Create a sample MathML element
        root = ET.fromstring('<math xmlns="http://www.w3.org/1998/Math/MathML" style="color:red"><mtext> test </mtext></math>')
        
        self.converter._optimize_mathml_structure(root)
        
        # Check that style attribute is removed
        assert 'style' not in root.attrib
        
        # Check that text content is stripped
        mtext = root.find('.//{http://www.w3.org/1998/Math/MathML}mtext')
        if mtext is not None:
            assert mtext.text == 'test'


class TestMathMLElement:
    """Test cases for MathMLElement class."""
    
    def test_mathml_element_creation(self):
        """Test MathMLElement creation."""
        element = MathMLElement(
            mathml_content='<math><mi>x</mi></math>',
            display_type='inline',
            original_latex='x',
            element_id='math_1'
        )
        
        assert element.mathml_content == '<math><mi>x</mi></math>'
        assert element.display_type == 'inline'
        assert element.original_latex == 'x'
        assert element.element_id == 'math_1'
    
    def test_mathml_element_defaults(self):
        """Test MathMLElement with default values."""
        element = MathMLElement(
            mathml_content='<math><mi>y</mi></math>',
            display_type='block',
            original_latex='y'
        )
        
        assert element.element_id is None


# Integration tests
class TestMathConverterIntegration:
    """Integration tests for math converter."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = MathConverter()
    
    @pytest.mark.skipif(
        not hasattr(MathConverter, '_convert_to_mathml'),
        reason="latex2mathml not available"
    )
    def test_real_conversion_simple(self):
        """Test real conversion of simple formulas."""
        element = ParsedElement(
            element_type='math',
            content='x^2 + y^2',
            attributes={'math_type': 'inline'},
            start_pos=0,
            end_pos=9,
            line_number=1
        )
        
        try:
            result = self.converter.convert_math_element(element)
            assert isinstance(result, MathMLElement)
            assert 'x' in result.mathml_content
            assert 'y' in result.mathml_content
        except ImportError:
            pytest.skip("latex2mathml not available")
    
    @pytest.mark.skipif(
        not hasattr(MathConverter, '_convert_to_mathml'),
        reason="latex2mathml not available"
    )
    def test_real_conversion_complex(self):
        """Test real conversion of complex formulas."""
        element = ParsedElement(
            element_type='math',
            content=r'\frac{a}{b} + \sqrt{c}',
            attributes={'math_type': 'display'},
            start_pos=0,
            end_pos=20,
            line_number=1
        )
        
        try:
            result = self.converter.convert_math_element(element)
            assert isinstance(result, MathMLElement)
            assert result.display_type == 'block'
        except ImportError:
            pytest.skip("latex2mathml not available")
