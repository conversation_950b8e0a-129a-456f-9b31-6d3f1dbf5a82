"""
Structure converter for LaTeX to Word conversion.

This module handles the conversion of LaTeX document structure elements
to Word-compatible format while preserving hierarchy and formatting.
"""

from typing import Dict, List, Optional, Any
from ..core.parser import ParsedElement
from ..core.exceptions import ConversionError


class StructureConverter:
    """Converter for LaTeX document structure elements."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the structure converter.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.section_numbering = config.get('styles', {}).get('section', {}).get('numbering', True)
        self.equation_numbering = config.get('styles', {}).get('equation', {}).get('numbering', True)
        
        # Counters for numbering
        self.section_counters = [0] * 7  # For different section levels
        self.equation_counter = 0
        self.figure_counter = 0
        self.table_counter = 0
    
    def convert_element(self, element: ParsedElement) -> ParsedElement:
        """
        Convert a parsed element to Word-compatible format.
        
        Args:
            element: ParsedElement to convert
            
        Returns:
            Converted ParsedElement
        """
        if element.element_type == 'section':
            return self._convert_section(element)
        elif element.element_type in ['math', 'math_environment']:
            return self._convert_math(element)
        elif element.element_type == 'list':
            return self._convert_list(element)
        elif element.element_type == 'table':
            return self._convert_table(element)
        elif element.element_type == 'figure':
            return self._convert_figure(element)
        elif element.element_type == 'formatted_text':
            return self._convert_formatted_text(element)
        else:
            return element  # Return unchanged for other types
    
    def _convert_section(self, element: ParsedElement) -> ParsedElement:
        """Convert section elements."""
        level = element.attributes.get('level', 2)
        is_starred = element.attributes.get('starred', False)
        title = element.content
        
        # Update section counters
        if not is_starred and self.section_numbering:
            if level < len(self.section_counters):
                self.section_counters[level] += 1
                # Reset lower level counters
                for i in range(level + 1, len(self.section_counters)):
                    self.section_counters[i] = 0
            
            # Generate section number
            section_number = '.'.join(
                str(self.section_counters[i]) 
                for i in range(2, level + 1) 
                if self.section_counters[i] > 0
            )
            
            if section_number:
                title = f"{section_number} {title}"
        
        # Update element
        element.content = title
        element.attributes['numbered'] = not is_starred and self.section_numbering
        
        return element
    
    def _convert_math(self, element: ParsedElement) -> ParsedElement:
        """Convert mathematical elements."""
        math_type = element.attributes.get('math_type', 'inline')
        environment = element.attributes.get('environment', '')
        
        # Handle equation numbering
        if (math_type == 'display' or environment in ['equation', 'align', 'gather']) and \
           not environment.endswith('*') and self.equation_numbering:
            self.equation_counter += 1
            element.attributes['equation_number'] = self.equation_counter
            element.attributes['numbered'] = True
        else:
            element.attributes['numbered'] = False
        
        return element
    
    def _convert_list(self, element: ParsedElement) -> ParsedElement:
        """Convert list elements."""
        env_name = element.attributes.get('environment', 'itemize')
        
        # Parse list items
        list_items = self._parse_list_items(element.content)
        
        # Determine list type
        if env_name == 'enumerate':
            list_type = 'ordered'
        elif env_name == 'itemize':
            list_type = 'unordered'
        elif env_name == 'description':
            list_type = 'description'
        else:
            list_type = 'unordered'
        
        element.attributes['list_type'] = list_type
        element.attributes['items'] = list_items
        
        return element
    
    def _parse_list_items(self, content: str) -> List[Dict[str, str]]:
        """Parse list items from content."""
        import re
        
        items = []
        
        # Pattern to match \item commands
        item_pattern = r'\\item(?:\[([^\]]*)\])?\s*(.*?)(?=\\item|\Z)'
        matches = re.finditer(item_pattern, content, re.DOTALL)
        
        for match in matches:
            label = match.group(1) if match.group(1) else None
            text = match.group(2).strip()
            
            items.append({
                'label': label,
                'text': text
            })
        
        return items
    
    def _convert_table(self, element: ParsedElement) -> ParsedElement:
        """Convert table elements."""
        env_name = element.attributes.get('environment', 'table')
        
        if env_name in ['table', 'longtable']:
            self.table_counter += 1
            element.attributes['table_number'] = self.table_counter
            element.attributes['numbered'] = True
        
        # Parse table structure (simplified)
        table_data = self._parse_table_content(element.content)
        element.attributes['table_data'] = table_data
        
        return element
    
    def _parse_table_content(self, content: str) -> Dict[str, Any]:
        """Parse table content structure."""
        # Simplified table parsing
        # In a full implementation, this would parse tabular environments
        return {
            'rows': [],
            'columns': 0,
            'caption': '',
            'label': ''
        }
    
    def _convert_figure(self, element: ParsedElement) -> ParsedElement:
        """Convert figure elements."""
        self.figure_counter += 1
        element.attributes['figure_number'] = self.figure_counter
        element.attributes['numbered'] = True
        
        # Parse figure content
        figure_data = self._parse_figure_content(element.content)
        element.attributes['figure_data'] = figure_data
        
        return element
    
    def _parse_figure_content(self, content: str) -> Dict[str, Any]:
        """Parse figure content."""
        import re
        
        # Extract caption
        caption_match = re.search(r'\\caption\{([^}]*)\}', content)
        caption = caption_match.group(1) if caption_match else ''
        
        # Extract label
        label_match = re.search(r'\\label\{([^}]*)\}', content)
        label = label_match.group(1) if label_match else ''
        
        # Extract includegraphics
        graphics_match = re.search(r'\\includegraphics(?:\[[^\]]*\])?\{([^}]*)\}', content)
        image_path = graphics_match.group(1) if graphics_match else ''
        
        return {
            'caption': caption,
            'label': label,
            'image_path': image_path
        }
    
    def _convert_formatted_text(self, element: ParsedElement) -> ParsedElement:
        """Convert formatted text elements."""
        format_type = element.attributes.get('format_type', 'unknown')
        
        # Map LaTeX formatting to Word formatting
        format_mapping = {
            'bold': {'bold': True},
            'italic': {'italic': True},
            'monospace': {'font_family': 'Courier New'},
            'small_caps': {'small_caps': True},
            'emphasis': {'italic': True},
            'underline': {'underline': True}
        }
        
        word_format = format_mapping.get(format_type, {})
        element.attributes['word_format'] = word_format
        
        return element
    
    def reset_counters(self) -> None:
        """Reset all counters to initial state."""
        self.section_counters = [0] * 7
        self.equation_counter = 0
        self.figure_counter = 0
        self.table_counter = 0
    
    def get_counters(self) -> Dict[str, Any]:
        """Get current counter values."""
        return {
            'sections': self.section_counters.copy(),
            'equations': self.equation_counter,
            'figures': self.figure_counter,
            'tables': self.table_counter
        }
