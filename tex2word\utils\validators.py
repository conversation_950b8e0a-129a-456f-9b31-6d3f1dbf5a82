"""
Validation utilities for tex2word package.

This module provides validation functions for LaTeX content,
mathematical expressions, and configuration settings.
"""

import re
from typing import Dict, List, Optional, Any, <PERSON><PERSON>


def validate_latex_syntax(content: str) -> Dict[str, Any]:
    """
    Validate basic LaTeX syntax.
    
    Args:
        content: LaTeX content to validate
        
    Returns:
        Dictionary with validation results
    """
    results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'statistics': {}
    }
    
    try:
        # Check for balanced braces
        brace_errors = _check_balanced_braces(content)
        results['errors'].extend(brace_errors)
        
        # Check for balanced environments
        env_errors = _check_balanced_environments(content)
        results['errors'].extend(env_errors)
        
        # Check for common syntax issues
        syntax_warnings = _check_common_syntax_issues(content)
        results['warnings'].extend(syntax_warnings)
        
        # Generate statistics
        results['statistics'] = _generate_syntax_statistics(content)
        
        # Set overall validity
        results['valid'] = len(results['errors']) == 0
        
    except Exception as e:
        results['valid'] = False
        results['errors'].append(f"Validation error: {str(e)}")
    
    return results


def validate_math_syntax(math_content: str) -> List[str]:
    """
    Validate mathematical LaTeX syntax.
    
    Args:
        math_content: Mathematical LaTeX content
        
    Returns:
        List of validation issues
    """
    issues = []
    
    # Check for balanced braces in math
    brace_issues = _check_balanced_braces(math_content)
    issues.extend(brace_issues)
    
    # Check for balanced delimiters
    delimiter_issues = _check_math_delimiters(math_content)
    issues.extend(delimiter_issues)
    
    # Check for common math command issues
    command_issues = _check_math_commands(math_content)
    issues.extend(command_issues)
    
    return issues


def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration dictionary.
    
    Args:
        config: Configuration to validate
        
    Returns:
        Dictionary with validation results
    """
    results = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Validate math configuration
    if 'math' in config:
        math_errors = _validate_math_config(config['math'])
        results['errors'].extend(math_errors)
    
    # Validate document configuration
    if 'document' in config:
        doc_errors = _validate_document_config(config['document'])
        results['errors'].extend(doc_errors)
    
    # Validate styles configuration
    if 'styles' in config:
        style_warnings = _validate_styles_config(config['styles'])
        results['warnings'].extend(style_warnings)
    
    # Validate output configuration
    if 'output' in config:
        output_errors = _validate_output_config(config['output'])
        results['errors'].extend(output_errors)
    
    results['valid'] = len(results['errors']) == 0
    return results


def _check_balanced_braces(content: str) -> List[str]:
    """Check for balanced braces in content."""
    errors = []
    brace_stack = []
    
    i = 0
    while i < len(content):
        char = content[i]
        
        if char == '\\' and i + 1 < len(content):
            # Skip escaped characters
            i += 2
            continue
        
        if char == '{':
            brace_stack.append(i)
        elif char == '}':
            if not brace_stack:
                errors.append(f"Unmatched closing brace at position {i}")
            else:
                brace_stack.pop()
        
        i += 1
    
    # Check for unmatched opening braces
    for pos in brace_stack:
        errors.append(f"Unmatched opening brace at position {pos}")
    
    return errors


def _check_balanced_environments(content: str) -> List[str]:
    """Check for balanced LaTeX environments."""
    errors = []
    env_stack = []
    
    # Find all \begin and \end commands
    begin_pattern = r'\\begin\s*\{([^}]+)\}'
    end_pattern = r'\\end\s*\{([^}]+)\}'
    
    begin_matches = list(re.finditer(begin_pattern, content))
    end_matches = list(re.finditer(end_pattern, content))
    
    # Combine and sort by position
    all_matches = []
    for match in begin_matches:
        all_matches.append(('begin', match.group(1), match.start()))
    for match in end_matches:
        all_matches.append(('end', match.group(1), match.start()))
    
    all_matches.sort(key=lambda x: x[2])
    
    # Check balance
    for match_type, env_name, pos in all_matches:
        if match_type == 'begin':
            env_stack.append((env_name, pos))
        elif match_type == 'end':
            if not env_stack:
                errors.append(f"Unmatched \\end{{{env_name}}} at position {pos}")
            else:
                last_env, last_pos = env_stack.pop()
                if last_env != env_name:
                    errors.append(f"Environment mismatch: \\begin{{{last_env}}} at {last_pos} "
                                f"closed with \\end{{{env_name}}} at {pos}")
    
    # Check for unclosed environments
    for env_name, pos in env_stack:
        errors.append(f"Unclosed environment \\begin{{{env_name}}} at position {pos}")
    
    return errors


def _check_common_syntax_issues(content: str) -> List[str]:
    """Check for common LaTeX syntax issues."""
    warnings = []
    
    # Check for double backslashes not in math mode
    double_backslash_pattern = r'(?<!\\)\\\\(?![\\{}])'
    matches = re.finditer(double_backslash_pattern, content)
    for match in matches:
        # Check if it's in math mode (simplified check)
        before = content[:match.start()]
        math_starts = before.count('$') + before.count('\\[') + before.count('\\(')
        math_ends = before.count('$') + before.count('\\]') + before.count('\\)')
        
        if math_starts == math_ends:  # Not in math mode
            warnings.append(f"Double backslash outside math mode at position {match.start()}")
    
    # Check for common typos
    typo_patterns = [
        (r'\\beginn\{', "Typo: \\beginn should be \\begin"),
        (r'\\endd\{', "Typo: \\endd should be \\end"),
        (r'\\secton\{', "Typo: \\secton should be \\section"),
    ]
    
    for pattern, message in typo_patterns:
        if re.search(pattern, content):
            warnings.append(message)
    
    return warnings


def _check_math_delimiters(math_content: str) -> List[str]:
    """Check for balanced math delimiters."""
    issues = []
    
    # Check for balanced parentheses
    paren_count = 0
    for i, char in enumerate(math_content):
        if char == '(' and (i == 0 or math_content[i-1] != '\\'):
            paren_count += 1
        elif char == ')' and (i == 0 or math_content[i-1] != '\\'):
            paren_count -= 1
            if paren_count < 0:
                issues.append(f"Unmatched closing parenthesis at position {i}")
    
    if paren_count > 0:
        issues.append(f"Unmatched opening parentheses: {paren_count}")
    
    # Check for balanced square brackets
    bracket_count = 0
    for i, char in enumerate(math_content):
        if char == '[' and (i == 0 or math_content[i-1] != '\\'):
            bracket_count += 1
        elif char == ']' and (i == 0 or math_content[i-1] != '\\'):
            bracket_count -= 1
            if bracket_count < 0:
                issues.append(f"Unmatched closing bracket at position {i}")
    
    if bracket_count > 0:
        issues.append(f"Unmatched opening brackets: {bracket_count}")
    
    return issues


def _check_math_commands(math_content: str) -> List[str]:
    """Check for common math command issues."""
    issues = []
    
    # Check for \frac without proper arguments
    frac_pattern = r'\\frac(?!\s*\{[^}]*\}\s*\{[^}]*\})'
    if re.search(frac_pattern, math_content):
        issues.append("\\frac command requires two arguments: \\frac{numerator}{denominator}")
    
    # Check for \sqrt without argument
    sqrt_pattern = r'\\sqrt(?!\s*(?:\[[^\]]*\])?\s*\{[^}]*\})'
    if re.search(sqrt_pattern, math_content):
        issues.append("\\sqrt command requires an argument: \\sqrt{expression}")
    
    # Check for incomplete superscripts/subscripts
    incomplete_sup_pattern = r'\^(?!\s*[\{a-zA-Z0-9])'
    if re.search(incomplete_sup_pattern, math_content):
        issues.append("Incomplete superscript: ^ should be followed by expression")
    
    incomplete_sub_pattern = r'_(?!\s*[\{a-zA-Z0-9])'
    if re.search(incomplete_sub_pattern, math_content):
        issues.append("Incomplete subscript: _ should be followed by expression")
    
    return issues


def _validate_math_config(math_config: Dict[str, Any]) -> List[str]:
    """Validate math configuration section."""
    errors = []
    
    # Check conversion method
    if 'conversion_method' in math_config:
        valid_methods = ['mathml', 'unicode', 'image']
        if math_config['conversion_method'] not in valid_methods:
            errors.append(f"Invalid math conversion method: {math_config['conversion_method']}")
    
    # Check custom commands
    if 'custom_commands' in math_config:
        if not isinstance(math_config['custom_commands'], dict):
            errors.append("custom_commands must be a dictionary")
    
    return errors


def _validate_document_config(doc_config: Dict[str, Any]) -> List[str]:
    """Validate document configuration section."""
    errors = []
    
    # Check page size
    if 'page_size' in doc_config:
        valid_sizes = ['A4', 'Letter', 'Legal', 'A3', 'A5']
        if doc_config['page_size'] not in valid_sizes:
            errors.append(f"Invalid page size: {doc_config['page_size']}")
    
    # Check font size
    if 'font_size' in doc_config:
        try:
            size = float(doc_config['font_size'])
            if size < 6 or size > 72:
                errors.append(f"Font size out of range (6-72): {size}")
        except (ValueError, TypeError):
            errors.append(f"Invalid font size: {doc_config['font_size']}")
    
    return errors


def _validate_styles_config(styles_config: Dict[str, Any]) -> List[str]:
    """Validate styles configuration section."""
    warnings = []
    
    # Check for unknown style properties
    known_properties = {
        'font_name', 'font_size', 'bold', 'italic', 'underline',
        'alignment', 'space_before', 'space_after', 'line_spacing',
        'numbering', 'color'
    }
    
    for style_name, style_props in styles_config.items():
        if isinstance(style_props, dict):
            for prop in style_props:
                if prop not in known_properties:
                    warnings.append(f"Unknown style property '{prop}' in style '{style_name}'")
    
    return warnings


def _validate_output_config(output_config: Dict[str, Any]) -> List[str]:
    """Validate output configuration section."""
    errors = []
    
    # Check format
    if 'format' in output_config:
        valid_formats = ['docx']
        if output_config['format'] not in valid_formats:
            errors.append(f"Invalid output format: {output_config['format']}")
    
    # Check compatibility mode
    if 'compatibility_mode' in output_config:
        valid_modes = ['word2016', 'word2019', 'word365']
        if output_config['compatibility_mode'] not in valid_modes:
            errors.append(f"Invalid compatibility mode: {output_config['compatibility_mode']}")
    
    return errors


def _generate_syntax_statistics(content: str) -> Dict[str, int]:
    """Generate syntax statistics for content."""
    stats = {}
    
    # Count braces
    stats['opening_braces'] = content.count('{')
    stats['closing_braces'] = content.count('}')
    
    # Count environments
    stats['begin_commands'] = len(re.findall(r'\\begin\s*\{', content))
    stats['end_commands'] = len(re.findall(r'\\end\s*\{', content))
    
    # Count commands
    stats['commands'] = len(re.findall(r'\\[a-zA-Z]+', content))
    
    # Count math delimiters
    stats['dollar_signs'] = content.count('$')
    stats['display_math'] = len(re.findall(r'\\\[|\\\]', content))
    
    return stats
