#!/usr/bin/env python3
"""
Test runner script for tex2word package.

This script provides a simple way to run tests and check the package
functionality without requiring pytest installation.
"""

import sys
import os
import traceback
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        import tex2word
        print("✅ tex2word package imported successfully")
        
        from tex2word import TexToWordConverter
        print("✅ TexToWordConverter imported successfully")
        
        from tex2word.core.parser import BaseParser, ParsedElement
        print("✅ Core parser classes imported successfully")
        
        from tex2word.parsers import DocumentParser, MathParser, ContentParser
        print("✅ Parser classes imported successfully")
        
        from tex2word.converters import MathConverter, StructureConverter
        print("✅ Converter classes imported successfully")
        
        from tex2word.generators import DocxGenerator
        print("✅ Generator classes imported successfully")
        
        from tex2word.config import ConfigManager
        print("✅ Configuration classes imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality without external dependencies."""
    print("\n🔍 Testing basic functionality...")
    
    try:
        from tex2word.core.parser import ParsedElement
        from tex2word.parsers.math_parser import MathParser
        from tex2word.parsers.document_parser import DocumentParser
        from tex2word.config.settings import ConfigManager
        
        # Test ParsedElement creation
        element = ParsedElement(
            element_type='test',
            content='test content',
            attributes={'test': True},
            start_pos=0,
            end_pos=10,
            line_number=1
        )
        print("✅ ParsedElement creation works")
        
        # Test MathParser initialization
        math_parser = MathParser()
        print("✅ MathParser initialization works")
        
        # Test DocumentParser initialization
        doc_parser = DocumentParser()
        print("✅ DocumentParser initialization works")
        
        # Test ConfigManager
        config_manager = ConfigManager()
        config = config_manager.get_config()
        assert isinstance(config, dict)
        print("✅ ConfigManager works")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False


def test_math_parsing():
    """Test mathematical content parsing."""
    print("\n🔍 Testing math parsing...")
    
    try:
        from tex2word.parsers.math_parser import MathParser
        
        parser = MathParser()
        
        # Test simple inline math
        latex_content = "This is $x + y = z$ inline math."
        parser.set_text(latex_content)
        elements = parser.parse()
        
        math_elements = [e for e in elements if e.element_type == 'math']
        if len(math_elements) > 0:
            print("✅ Inline math parsing works")
        else:
            print("⚠️  No math elements found in inline math test")
        
        # Test display math
        latex_content = r"\begin{equation} E = mc^2 \end{equation}"
        parser.set_text(latex_content)
        elements = parser.parse()
        
        math_env_elements = [e for e in elements if e.element_type == 'math_environment']
        if len(math_env_elements) > 0:
            print("✅ Display math parsing works")
        else:
            print("⚠️  No math environment elements found")
        
        return True
        
    except Exception as e:
        print(f"❌ Math parsing test failed: {e}")
        traceback.print_exc()
        return False


def test_document_parsing():
    """Test document structure parsing."""
    print("\n🔍 Testing document parsing...")
    
    try:
        from tex2word.parsers.document_parser import DocumentParser
        
        parser = DocumentParser()
        
        # Test section parsing
        latex_content = r"""
        \documentclass{article}
        \title{Test Document}
        \author{Test Author}
        \begin{document}
        \section{Introduction}
        \subsection{Background}
        \end{document}
        """
        
        parser.set_text(latex_content)
        elements = parser.parse()
        
        section_elements = [e for e in elements if e.element_type == 'section']
        if len(section_elements) >= 2:  # section and subsection
            print("✅ Document structure parsing works")
        else:
            print(f"⚠️  Expected 2+ section elements, found {len(section_elements)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Document parsing test failed: {e}")
        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration management."""
    print("\n🔍 Testing configuration...")
    
    try:
        from tex2word.config.settings import ConfigManager, DEFAULT_CONFIG
        
        # Test default configuration
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        assert 'math' in config
        assert 'document' in config
        assert 'styles' in config
        print("✅ Default configuration loaded")
        
        # Test configuration update
        custom_config = {
            'math': {'conversion_method': 'unicode'},
            'document': {'font_size': 14}
        }
        
        config_manager.update_config(custom_config)
        updated_config = config_manager.get_config()
        
        assert updated_config['math']['conversion_method'] == 'unicode'
        assert updated_config['document']['font_size'] == 14
        print("✅ Configuration update works")
        
        # Test value access
        value = config_manager.get_value('math.conversion_method')
        assert value == 'unicode'
        print("✅ Configuration value access works")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False


def test_utilities():
    """Test utility functions."""
    print("\n🔍 Testing utilities...")
    
    try:
        from tex2word.utils.helpers import (
            clean_latex_content, normalize_whitespace, 
            extract_document_metadata, count_latex_elements
        )
        from tex2word.utils.validators import validate_latex_syntax, validate_math_syntax
        
        # Test content cleaning
        dirty_content = "  This   has   extra   spaces  \n\n\n\n  "
        clean_content = normalize_whitespace(dirty_content)
        assert "extra   spaces" not in clean_content
        print("✅ Content normalization works")
        
        # Test metadata extraction
        latex_with_metadata = r"""
        \documentclass{article}
        \title{Test Title}
        \author{Test Author}
        \date{2024}
        \begin{document}
        Content here
        \end{document}
        """
        
        metadata = extract_document_metadata(latex_with_metadata)
        assert metadata['title'] == 'Test Title'
        assert metadata['author'] == 'Test Author'
        print("✅ Metadata extraction works")
        
        # Test element counting
        latex_content = r"""
        \section{Test}
        \subsection{Test Sub}
        $x = y$ and \begin{equation} E = mc^2 \end{equation}
        """
        
        counts = count_latex_elements(latex_content)
        assert counts['sections'] >= 1
        assert counts['subsections'] >= 1
        print("✅ Element counting works")
        
        # Test validation
        valid_latex = r"\section{Test} This is valid."
        validation_result = validate_latex_syntax(valid_latex)
        assert validation_result['valid'] is True
        print("✅ LaTeX validation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Utilities test failed: {e}")
        traceback.print_exc()
        return False


def test_converter_initialization():
    """Test converter initialization."""
    print("\n🔍 Testing converter initialization...")
    
    try:
        from tex2word import TexToWordConverter
        
        # Test basic initialization
        converter = TexToWordConverter()
        assert converter.config is not None
        print("✅ Basic converter initialization works")
        
        # Test initialization with custom config
        custom_config = {
            'math': {'conversion_method': 'unicode'},
            'document': {'font_size': 12}
        }
        
        converter_custom = TexToWordConverter(custom_config=custom_config)
        assert converter_custom.config['math']['conversion_method'] == 'unicode'
        print("✅ Custom config converter initialization works")
        
        return True
        
    except Exception as e:
        print(f"❌ Converter initialization test failed: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Running tex2word tests...\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Math Parsing", test_math_parsing),
        ("Document Parsing", test_document_parsing),
        ("Configuration", test_configuration),
        ("Utilities", test_utilities),
        ("Converter Initialization", test_converter_initialization),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED\n")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}\n")
    
    # Summary
    total = passed + failed
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"⚠️  {failed} test(s) failed")
        return False


def main():
    """Main function."""
    success = run_all_tests()
    
    if success:
        print("\n💡 Next steps:")
        print("   1. Try the demo: python examples/demo.py")
        print("   2. Install dependencies: pip install -e .")
        print("   3. Run full tests: pytest")
        print("   4. Try CLI: python -m tex2word.cli --help")
        sys.exit(0)
    else:
        print("\n🔧 Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
