"""
Style converter for LaTeX to Word conversion.

This module handles the conversion of LaTeX styling and formatting
to Word-compatible styles and formatting.
"""

from typing import Dict, List, Optional, Any
from ..core.parser import ParsedElement


class StyleConverter:
    """Converter for LaTeX styling to Word formatting."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the style converter.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.styles_config = config.get('styles', {})
    
    def convert_text_formatting(self, element: ParsedElement) -> Dict[str, Any]:
        """
        Convert LaTeX text formatting to Word formatting.
        
        Args:
            element: ParsedElement with formatting information
            
        Returns:
            Dictionary with Word formatting properties
        """
        format_type = element.attributes.get('format_type', 'normal')
        
        # Base formatting
        formatting = {
            'bold': False,
            'italic': False,
            'underline': False,
            'font_family': None,
            'font_size': None,
            'color': None
        }
        
        # Apply specific formatting based on type
        if format_type == 'bold':
            formatting['bold'] = True
        elif format_type == 'italic':
            formatting['italic'] = True
        elif format_type == 'monospace':
            formatting['font_family'] = 'Courier New'
        elif format_type == 'underline':
            formatting['underline'] = True
        elif format_type == 'emphasis':
            formatting['italic'] = True
        
        return formatting
    
    def get_section_style(self, level: int) -> Dict[str, Any]:
        """
        Get Word style for section heading.
        
        Args:
            level: Section level (1=chapter, 2=section, etc.)
            
        Returns:
            Dictionary with style properties
        """
        if level == 2:  # \section
            return self.styles_config.get('section', {})
        elif level == 3:  # \subsection
            return self.styles_config.get('subsection', {})
        else:
            # Default heading style
            return {
                'font_size': max(14 - level, 10),
                'bold': True,
                'space_before': '6pt',
                'space_after': '3pt'
            }
    
    def get_paragraph_style(self) -> Dict[str, Any]:
        """Get Word style for normal paragraphs."""
        return self.styles_config.get('normal', {})
    
    def get_equation_style(self) -> Dict[str, Any]:
        """Get Word style for equations."""
        return self.styles_config.get('equation', {})
    
    def get_list_style(self, list_type: str) -> Dict[str, Any]:
        """Get Word style for lists."""
        base_style = self.styles_config.get('list', {})
        
        if list_type == 'ordered':
            base_style['numbering'] = True
        elif list_type == 'unordered':
            base_style['bullets'] = True
        
        return base_style
