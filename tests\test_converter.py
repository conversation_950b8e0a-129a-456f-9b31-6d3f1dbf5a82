"""
Tests for the main converter module.

This module contains comprehensive tests for the main TexToWordConverter class,
including end-to-end conversion testing.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from tex2word.core.converter import TexToWordConverter
from tex2word.core.exceptions import TexToWordError, ConversionError
from tex2word.core.parser import ParsedElement


class TestTexToWordConverter:
    """Test cases for TexToWordConverter class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = TexToWordConverter()
    
    def test_initialization_default(self):
        """Test converter initialization with defaults."""
        assert self.converter.config is not None
        assert self.converter.document_parser is not None
        assert self.converter.math_parser is not None
        assert self.converter.content_parser is not None
        assert self.converter.math_converter is not None
        assert self.converter.structure_converter is not None
        assert self.converter.docx_generator is not None
    
    def test_initialization_with_config(self):
        """Test converter initialization with custom config."""
        custom_config = {
            'math': {'conversion_method': 'unicode'},
            'document': {'font_size': 12}
        }
        
        converter = TexToWordConverter(custom_config=custom_config)
        assert converter.config['math']['conversion_method'] == 'unicode'
        assert converter.config['document']['font_size'] == 12
    
    def test_validate_inputs_valid(self):
        """Test input validation with valid inputs."""
        with tempfile.NamedTemporaryFile(suffix='.tex', delete=False) as temp_tex:
            temp_tex.write(b'\\documentclass{article}\\begin{document}Test\\end{document}')
            temp_tex_path = temp_tex.name
        
        try:
            output_path = temp_tex_path.replace('.tex', '.docx')
            # Should not raise any exception
            self.converter._validate_inputs(temp_tex_path, output_path)
        finally:
            os.unlink(temp_tex_path)
    
    def test_validate_inputs_missing_file(self):
        """Test input validation with missing input file."""
        with pytest.raises(ConversionError) as exc_info:
            self.converter._validate_inputs('nonexistent.tex', 'output.docx')
        
        assert "Input file not found" in str(exc_info.value)
    
    def test_validate_inputs_wrong_extension(self):
        """Test input validation with wrong file extensions."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file_path = temp_file.name
        
        try:
            with pytest.raises(ConversionError) as exc_info:
                self.converter._validate_inputs(temp_file_path, 'output.docx')
            
            assert "must be a LaTeX file" in str(exc_info.value)
        finally:
            os.unlink(temp_file_path)
    
    def test_validate_inputs_wrong_output_extension(self):
        """Test input validation with wrong output extension."""
        with tempfile.NamedTemporaryFile(suffix='.tex', delete=False) as temp_tex:
            temp_tex_path = temp_tex.name
        
        try:
            with pytest.raises(ConversionError) as exc_info:
                self.converter._validate_inputs(temp_tex_path, 'output.txt')
            
            assert "must be a Word document" in str(exc_info.value)
        finally:
            os.unlink(temp_tex_path)
    
    def test_read_latex_file_utf8(self):
        """Test reading LaTeX file with UTF-8 encoding."""
        content = '\\documentclass{article}\n\\begin{document}\nHello, 世界!\n\\end{document}'
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.tex', encoding='utf-8', delete=False) as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            read_content = self.converter._read_latex_file(temp_file_path)
            assert read_content == content
        finally:
            os.unlink(temp_file_path)
    
    def test_read_latex_file_latin1_fallback(self):
        """Test reading LaTeX file with Latin-1 fallback."""
        content = '\\documentclass{article}\n\\begin{document}\nHello!\n\\end{document}'
        
        with tempfile.NamedTemporaryFile(mode='wb', suffix='.tex', delete=False) as temp_file:
            temp_file.write(content.encode('latin-1'))
            temp_file_path = temp_file.name
        
        try:
            read_content = self.converter._read_latex_file(temp_file_path)
            assert 'Hello!' in read_content
        finally:
            os.unlink(temp_file_path)
    
    def test_read_latex_file_error(self):
        """Test reading non-existent LaTeX file."""
        with pytest.raises(ConversionError) as exc_info:
            self.converter._read_latex_file('nonexistent.tex')
        
        assert "Cannot read input file" in str(exc_info.value)
    
    @patch('tex2word.core.converter.TexToWordConverter._parse_document')
    @patch('tex2word.core.converter.TexToWordConverter._convert_elements')
    @patch('tex2word.core.converter.TexToWordConverter._generate_word_document')
    def test_convert_string_success(self, mock_generate, mock_convert, mock_parse):
        """Test successful string conversion."""
        # Setup mocks
        mock_elements = [
            ParsedElement('section', 'Test Section', {}, 0, 10, 1),
            ParsedElement('text', 'Test content', {}, 11, 23, 2)
        ]
        mock_parse.return_value = mock_elements
        mock_convert.return_value = mock_elements
        
        latex_content = '\\section{Test Section}\nTest content'
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_output:
            output_path = temp_output.name
        
        try:
            result = self.converter.convert_string(latex_content, output_path)
            
            assert result['success'] is True
            assert result['output_file'] == output_path
            assert 'statistics' in result
            assert 'warnings' in result
            
            mock_parse.assert_called_once_with(latex_content)
            mock_convert.assert_called_once_with(mock_elements)
            mock_generate.assert_called_once_with(mock_elements, output_path)
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_parse_document(self):
        """Test document parsing."""
        latex_content = '''
        \\documentclass{article}
        \\begin{document}
        \\section{Introduction}
        This is a test document with $x = y$ inline math.
        \\begin{equation}
        E = mc^2
        \\end{equation}
        \\end{document}
        '''
        
        elements = self.converter._parse_document(latex_content)
        
        assert isinstance(elements, list)
        assert len(elements) > 0
        
        # Check that elements are sorted by position
        for i in range(1, len(elements)):
            assert elements[i-1].start_pos <= elements[i].start_pos
    
    def test_convert_elements(self):
        """Test element conversion."""
        elements = [
            ParsedElement('section', 'Test Section', {'level': 2}, 0, 10, 1),
            ParsedElement('math', 'x + y', {'math_type': 'inline'}, 11, 16, 2),
            ParsedElement('text', 'Regular text', {}, 17, 29, 3)
        ]
        
        with patch.object(self.converter.math_converter, 'convert_math_element') as mock_math_convert:
            mock_math_convert.return_value = Mock()
            
            converted = self.converter._convert_elements(elements)
            
            assert len(converted) == len(elements)
            mock_math_convert.assert_called_once()
    
    def test_convert_elements_with_error(self):
        """Test element conversion with error handling."""
        elements = [
            ParsedElement('math', 'invalid_math', {'math_type': 'inline'}, 0, 12, 1)
        ]
        
        with patch.object(self.converter.math_converter, 'convert_math_element') as mock_math_convert:
            mock_math_convert.side_effect = Exception("Conversion failed")
            
            converted = self.converter._convert_elements(elements)
            
            assert len(converted) == 1
            assert self.converter.stats['warnings'] > 0
    
    def test_set_config(self):
        """Test configuration update."""
        new_config = {
            'math': {'conversion_method': 'unicode'},
            'document': {'font_size': 14}
        }
        
        self.converter.set_config(new_config)
        
        assert self.converter.config['math']['conversion_method'] == 'unicode'
        assert self.converter.config['document']['font_size'] == 14
    
    def test_get_config(self):
        """Test configuration retrieval."""
        config = self.converter.get_config()
        
        assert isinstance(config, dict)
        assert 'math' in config
        assert 'document' in config
        assert 'styles' in config
    
    def test_validate_latex_valid(self):
        """Test LaTeX validation with valid content."""
        latex_content = '''
        \\documentclass{article}
        \\begin{document}
        \\section{Test}
        Simple content with $x = y$.
        \\end{document}
        '''
        
        results = self.converter.validate_latex(latex_content)
        
        assert results['valid'] is True
        assert isinstance(results['errors'], list)
        assert isinstance(results['warnings'], list)
        assert isinstance(results['statistics'], dict)
    
    def test_validate_latex_invalid(self):
        """Test LaTeX validation with invalid content."""
        # Create content that will cause parsing errors
        latex_content = '\\invalid_command{unclosed_brace'
        
        with patch.object(self.converter.document_parser, 'parse') as mock_parse:
            mock_parse.side_effect = Exception("Parse error")
            
            results = self.converter.validate_latex(latex_content)
            
            assert results['valid'] is False
            assert len(results['errors']) > 0
    
    def test_get_conversion_statistics(self):
        """Test conversion statistics retrieval."""
        # Set some test statistics
        self.converter.stats['total_elements'] = 10
        self.converter.stats['math_elements'] = 3
        
        with patch.object(self.converter.math_converter, 'get_conversion_statistics') as mock_math_stats:
            mock_math_stats.return_value = {'total_equations': 2}
            
            with patch.object(self.converter.docx_generator, 'get_document_statistics') as mock_doc_stats:
                mock_doc_stats.return_value = {'total_paragraphs': 5}
                
                stats = self.converter._get_conversion_statistics()
                
                assert stats['total_elements'] == 10
                assert stats['math_elements'] == 3
                assert stats['total_equations'] == 2
                assert stats['total_paragraphs'] == 5
    
    def test_add_warning(self):
        """Test warning addition."""
        self.converter._add_warning("Test warning")
        
        warnings = self.converter._get_warnings()
        assert len(warnings) == 1
        assert warnings[0] == "Test warning"
    
    def test_multiple_warnings(self):
        """Test multiple warning handling."""
        self.converter._add_warning("Warning 1")
        self.converter._add_warning("Warning 2")
        
        warnings = self.converter._get_warnings()
        assert len(warnings) == 2
        assert "Warning 1" in warnings
        assert "Warning 2" in warnings


class TestTexToWordConverterIntegration:
    """Integration tests for TexToWordConverter."""
    
    def test_simple_document_conversion(self):
        """Test conversion of a simple document."""
        latex_content = '''
        \\documentclass{article}
        \\title{Test Document}
        \\author{Test Author}
        \\begin{document}
        \\maketitle
        \\section{Introduction}
        This is a simple test document.
        \\end{document}
        '''
        
        converter = TexToWordConverter()
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_output:
            output_path = temp_output.name
        
        try:
            # Mock the actual file generation to avoid docx dependency issues in tests
            with patch.object(converter.docx_generator, 'generate_document'):
                result = converter.convert_string(latex_content, output_path)
                
                assert result['success'] is True
                assert 'statistics' in result
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_math_document_conversion(self):
        """Test conversion of a document with math."""
        latex_content = '''
        \\documentclass{article}
        \\begin{document}
        \\section{Mathematics}
        Inline math: $x + y = z$.
        
        Display math:
        \\begin{equation}
        E = mc^2
        \\end{equation}
        \\end{document}
        '''
        
        converter = TexToWordConverter()
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_output:
            output_path = temp_output.name
        
        try:
            with patch.object(converter.docx_generator, 'generate_document'):
                result = converter.convert_string(latex_content, output_path)
                
                assert result['success'] is True
                stats = result['statistics']
                assert stats['math_elements'] > 0
        finally:
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    @patch('tex2word.core.converter.TexToWordConverter._read_latex_file')
    def test_file_conversion(self, mock_read_file):
        """Test file-based conversion."""
        latex_content = '\\documentclass{article}\\begin{document}Test\\end{document}'
        mock_read_file.return_value = latex_content
        
        converter = TexToWordConverter()
        
        with tempfile.NamedTemporaryFile(suffix='.tex', delete=False) as temp_input:
            input_path = temp_input.name
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_output:
            output_path = temp_output.name
        
        try:
            with patch.object(converter.docx_generator, 'generate_document'):
                result = converter.convert(input_path, output_path)
                
                assert result['success'] is True
                assert result['input_file'] == input_path
                assert result['output_file'] == output_path
                mock_read_file.assert_called_once_with(input_path)
        finally:
            if os.path.exists(input_path):
                os.unlink(input_path)
            if os.path.exists(output_path):
                os.unlink(output_path)
